using HealthTrack.Data;
using HealthTrack.Models.Entities;
using Microsoft.EntityFrameworkCore;

namespace HealthTrack.Services
{
    public interface INotificationService
    {
        Task CreateNotificationAsync(string userId, string title, string message, NotificationType type, string? actionUrl = null, string? actionText = null);
        Task<List<Notification>> GetUserNotificationsAsync(string userId, int count = 10);
        Task<int> GetUnreadCountAsync(string userId);
        Task MarkAsReadAsync(int notificationId);
        Task MarkAllAsReadAsync(string userId);
        Task SendApprovalNotificationAsync(string userId);
        Task SendRejectionNotificationAsync(string userId, string reason = "");
        Task SendWelcomeNotificationAsync(string userId, string userRole);
    }

    public class NotificationService : INotificationService
    {
        private readonly ApplicationDbContext _context;

        public NotificationService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task CreateNotificationAsync(string userId, string title, string message, NotificationType type, string? actionUrl = null, string? actionText = null)
        {
            var notification = new Notification
            {
                UserId = userId,
                Title = title,
                Message = message,
                Type = type,
                ActionUrl = actionUrl,
                ActionText = actionText,
                CreatedAt = DateTime.UtcNow
            };

            _context.Notifications.Add(notification);
            await _context.SaveChangesAsync();
        }

        public async Task<List<Notification>> GetUserNotificationsAsync(string userId, int count = 10)
        {
            return await _context.Notifications
                .Where(n => n.UserId == userId)
                .OrderByDescending(n => n.CreatedAt)
                .Take(count)
                .ToListAsync();
        }

        public async Task<int> GetUnreadCountAsync(string userId)
        {
            return await _context.Notifications
                .Where(n => n.UserId == userId && !n.IsRead)
                .CountAsync();
        }

        public async Task MarkAsReadAsync(int notificationId)
        {
            var notification = await _context.Notifications.FindAsync(notificationId);
            if (notification != null && !notification.IsRead)
            {
                notification.IsRead = true;
                notification.ReadAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
        }

        public async Task MarkAllAsReadAsync(string userId)
        {
            var unreadNotifications = await _context.Notifications
                .Where(n => n.UserId == userId && !n.IsRead)
                .ToListAsync();

            foreach (var notification in unreadNotifications)
            {
                notification.IsRead = true;
                notification.ReadAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
        }

        public async Task SendApprovalNotificationAsync(string userId)
        {
            await CreateNotificationAsync(
                userId,
                "تم تفعيل حسابك",
                "مرحباً بك! تم الموافقة على حسابك وتفعيله بنجاح. يمكنك الآن الاستفادة من جميع خدمات النظام.",
                NotificationType.Approval,
                "/Patient",
                "الذهاب للوحة التحكم"
            );
        }

        public async Task SendRejectionNotificationAsync(string userId, string reason = "")
        {
            var message = "نأسف لإبلاغك أنه تم رفض طلب تفعيل حسابك.";
            if (!string.IsNullOrEmpty(reason))
            {
                message += $" السبب: {reason}";
            }
            message += " يمكنك التواصل مع الإدارة للمزيد من المعلومات.";

            await CreateNotificationAsync(
                userId,
                "تم رفض طلب التفعيل",
                message,
                NotificationType.Rejection,
                "/Home/Contact",
                "تواصل معنا"
            );
        }

        public async Task SendWelcomeNotificationAsync(string userId, string userRole)
        {
            string title = "مرحباً بك في HealthTrack";
            string message = userRole switch
            {
                "Admin" => "مرحباً بك كمدير للنظام. يمكنك إدارة جميع المستخدمين والسجلات الطبية.",
                "Employee" => "مرحباً بك كموظف في النظام. يمكنك تسجيل المرضى الجدد وإدارة السجلات الطبية.",
                "Patient" => "مرحباً بك كمريض في النظام. يمكنك الاطلاع على سجلاتك الطبية وزياراتك.",
                _ => "مرحباً بك في نظام HealthTrack لإدارة السجلات الطبية."
            };

            await CreateNotificationAsync(
                userId,
                title,
                message,
                NotificationType.Success,
                userRole == "Patient" ? "/Patient" : userRole == "Employee" ? "/Employee" : "/Admin",
                "الذهاب للوحة التحكم"
            );
        }
    }
}
