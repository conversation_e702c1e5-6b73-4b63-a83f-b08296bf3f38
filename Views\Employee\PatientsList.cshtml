@{
    ViewData["Title"] = "قائمة المرضى";
    var patients = ViewBag.Patients as List<HealthTrack.Models.Entities.ApplicationUser>;
    var currentPage = ViewBag.CurrentPage;
    var totalPages = ViewBag.TotalPages;
}

<div class="container-fluid py-4" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">قائمة المرضى</h6>
                    <a asp-action="RegisterPatient" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> تسجيل مريض جديد
                    </a>
                </div>
                <div class="card-body">
                    <!-- جدول المرضى -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الاسم الكامل</th>
                                    <th>رقم الهوية</th>
                                    <th>رقم الهاتف</th>
                                    <th>الجنس</th>
                                    <th>العمر</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (patients != null && patients.Any())
                                {
                                    @foreach (var patient in patients)
                                    {
                                        var age = DateTime.Now.Year - patient.DateOfBirth.Year;
                                        if (DateTime.Now.DayOfYear < patient.DateOfBirth.DayOfYear)
                                            age--;

                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-3">
                                                        <div class="avatar-title rounded-circle bg-primary text-white">
                                                            @patient.FirstName.Substring(0, 1)@patient.LastName.Substring(0, 1)
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">@patient.FullName</h6>
                                                        <small class="text-muted">@patient.Email</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>@patient.NationalId</td>
                                            <td>@patient.PhoneNumber</td>
                                            <td>
                                                @if (patient.Gender == HealthTrack.Models.Entities.Gender.Male)
                                                {
                                                    <span class="badge bg-info">ذكر</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-pink">أنثى</span>
                                                }
                                            </td>
                                            <td>@age سنة</td>
                                            <td>@patient.CreatedAt.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                @if (patient.IsActive && patient.IsApproved)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else if (!patient.IsApproved)
                                                {
                                                    <span class="badge bg-warning">في انتظار الموافقة</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">معطل</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary dropdown-toggle" 
                                                            data-bs-toggle="dropdown">
                                                        الإجراءات
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item" href="#" 
                                                               onclick="viewPatientDetails('@patient.Id')">
                                                                <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item" 
                                                               asp-action="CreateMedicalRecord" 
                                                               asp-route-patientId="@patient.Id">
                                                                <i class="fas fa-file-medical me-2"></i>إنشاء سجل طبي
                                                            </a>
                                                        </li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <a class="dropdown-item text-info" href="#"
                                                               onclick="viewMedicalHistory('@patient.Id')">
                                                                <i class="fas fa-history me-2"></i>السجل الطبي
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-users fa-3x mb-3"></i>
                                                <p>لا توجد بيانات مرضى</p>
                                                <a asp-action="RegisterPatient" class="btn btn-primary">
                                                    تسجيل أول مريض
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if (totalPages > 1)
                    {
                        <nav class="mt-4">
                            <ul class="pagination justify-content-center">
                                @for (int i = 1; i <= totalPages; i++)
                                {
                                    <li class="page-item @(i == currentPage ? "active" : "")">
                                        <a class="page-link" asp-action="PatientsList" asp-route-page="@i">@i</a>
                                    </li>
                                }
                            </ul>
                        </nav>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض تفاصيل المريض -->
<div class="modal fade" id="patientDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل المريض</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="patientDetailsContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.bg-pink {
    background-color: #e91e63 !important;
}
</style>

@section Scripts {
    <script>
        function viewPatientDetails(patientId) {
            // يمكن إضافة AJAX call لجلب تفاصيل المريض
            $('#patientDetailsModal').modal('show');
        }

        function viewMedicalHistory(patientId) {
            // يمكن إضافة AJAX call لجلب السجل الطبي
            window.location.href = '@Url.Action("ViewMedicalRecord", "Employee")' + '?patientId=' + patientId;
        }
    </script>
}
