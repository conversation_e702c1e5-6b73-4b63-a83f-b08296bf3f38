@{
    ViewData["Title"] = "لوحة تحكم المريض";
    var user = ViewBag.User as HealthTrack.Models.Entities.ApplicationUser;
    var medicalRecordsCount = ViewBag.MedicalRecordsCount;
    var lastVisit = ViewBag.LastVisit as HealthTrack.Models.Entities.MedicalVisit;
    var insuranceInfo = ViewBag.InsuranceInfo as HealthTrack.Models.Entities.InsuranceInfo;
}

<div class="container-fluid py-4" dir="rtl">
    <!-- ترحيب -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white shadow">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="mb-1">مرحباً، @user?.FullName</h2>
                            <p class="mb-0">نتمنى لك دوام الصحة والعافية</p>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-circle fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                سجلاتي الطبية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@medicalRecordsCount</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-medical fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                آخر زيارة
                            </div>
                            <div class="h6 mb-0 font-weight-bold text-gray-800">
                                @if (lastVisit != null)
                                {
                                    @lastVisit.VisitDate.ToString("dd/MM/yyyy")
                                }
                                else
                                {
                                    <span class="text-muted">لا توجد زيارات</span>
                                }
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                التأمين الصحي
                            </div>
                            <div class="h6 mb-0 font-weight-bold text-gray-800">
                                @if (insuranceInfo != null)
                                {
                                    <span class="text-success">مفعل</span>
                                }
                                else
                                {
                                    <span class="text-muted">غير مفعل</span>
                                }
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shield-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                العمر
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                @{
                                    var age = DateTime.Now.Year - user!.DateOfBirth.Year;
                                    if (DateTime.Now.DayOfYear < user.DateOfBirth.DayOfYear)
                                        age--;
                                }
                                @age سنة
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-birthday-cake fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإجراءات السريعة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الإجراءات السريعة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a asp-action="Profile" class="btn btn-primary btn-block">
                                <i class="fas fa-user"></i> الملف الشخصي
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-action="MedicalRecords" class="btn btn-success btn-block">
                                <i class="fas fa-file-medical-alt"></i> سجلاتي الطبية
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-action="MedicalVisits" class="btn btn-info btn-block">
                                <i class="fas fa-calendar-alt"></i> زياراتي الطبية
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="#contact" class="btn btn-warning btn-block">
                                <i class="fas fa-phone"></i> تواصل معنا
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- آخر الأنشطة -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">آخر زيارة طبية</h6>
                </div>
                <div class="card-body">
                    @if (lastVisit != null)
                    {
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>تاريخ الزيارة:</strong> @lastVisit.VisitDate.ToString("dd/MM/yyyy")</p>
                                <p><strong>الشكوى الرئيسية:</strong> @(lastVisit.ChiefComplaint ?? "غير محدد")</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>التشخيص:</strong> @(lastVisit.Diagnosis ?? "غير محدد")</p>
                                <p><strong>العلاج:</strong> @(lastVisit.Treatment ?? "غير محدد")</p>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a asp-action="ViewMedicalVisit" asp-route-id="@lastVisit.Id" class="btn btn-outline-primary btn-sm">
                                عرض التفاصيل الكاملة
                            </a>
                        </div>
                    }
                    else
                    {
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-calendar-times fa-3x mb-3"></i>
                            <p>لا توجد زيارات طبية مسجلة</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات التأمين</h6>
                </div>
                <div class="card-body">
                    @if (insuranceInfo != null)
                    {
                        <p><strong>شركة التأمين:</strong> @insuranceInfo.InsuranceCompany</p>
                        <p><strong>رقم البوليصة:</strong> @insuranceInfo.PolicyNumber</p>
                        @if (insuranceInfo.ExpiryDate.HasValue)
                        {
                            <p><strong>تاريخ الانتهاء:</strong> @insuranceInfo.ExpiryDate.Value.ToString("dd/MM/yyyy")</p>
                        }
                        <span class="badge bg-success">مفعل</span>
                    }
                    else
                    {
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-shield-alt fa-2x mb-2"></i>
                            <p>لا توجد معلومات تأمين</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-right-primary {
    border-right: 0.25rem solid #4e73df !important;
}
.border-right-success {
    border-right: 0.25rem solid #1cc88a !important;
}
.border-right-info {
    border-right: 0.25rem solid #36b9cc !important;
}
.border-right-warning {
    border-right: 0.25rem solid #f6c23e !important;
}
.bg-gradient-primary {
    background: linear-gradient(87deg, #5e72e4 0, #825ee4 100%) !important;
}
</style>
