@{
    ViewData["Title"] = "طلبات الموافقة";
    var usersWithRoles = ViewBag.UsersWithRoles as List<(HealthTrack.Models.Entities.ApplicationUser User, IList<string> Roles)>;
    var currentPage = ViewBag.CurrentPage;
    var totalPages = ViewBag.TotalPages;
}

<div class="container-fluid py-4" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-clock me-2"></i>طلبات الموافقة
                    </h6>
                    @if (usersWithRoles != null && usersWithRoles.Any())
                    {
                        <button type="button" class="btn btn-success btn-sm" onclick="approveSelected()">
                            <i class="fas fa-check-double"></i> موافقة على المحدد
                        </button>
                    }
                </div>
                <div class="card-body">
                    @if (usersWithRoles != null && usersWithRoles.Any())
                    {
                        <form id="bulkApprovalForm" asp-action="BulkApprove" method="post">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-warning">
                                        <tr>
                                            <th width="50">
                                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                            </th>
                                            <th>الاسم الكامل</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>رقم الهوية</th>
                                            <th>رقم الهاتف</th>
                                            <th>الدور</th>
                                            <th>تاريخ التسجيل</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var userWithRole in usersWithRoles)
                                        {
                                            <tr>
                                                <td>
                                                    <input type="checkbox" name="userIds" value="@userWithRole.User.Id" class="user-checkbox">
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm me-3">
                                                            <div class="avatar-title rounded-circle bg-warning text-white">
                                                                @userWithRole.User.FirstName.Substring(0, 1)@userWithRole.User.LastName.Substring(0, 1)
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-0">@userWithRole.User.FullName</h6>
                                                            <small class="text-muted">
                                                                @if (userWithRole.User.Gender == HealthTrack.Models.Entities.Gender.Male)
                                                                {
                                                                    <span>ذكر</span>
                                                                }
                                                                else
                                                                {
                                                                    <span>أنثى</span>
                                                                }
                                                                - @{
                                                                    var age = DateTime.Now.Year - userWithRole.User.DateOfBirth.Year;
                                                                    if (DateTime.Now.DayOfYear < userWithRole.User.DateOfBirth.DayOfYear)
                                                                        age--;
                                                                }
                                                                @age سنة
                                                            </small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>@userWithRole.User.Email</td>
                                                <td>@userWithRole.User.NationalId</td>
                                                <td>@userWithRole.User.PhoneNumber</td>
                                                <td>
                                                    @foreach (var role in userWithRole.Roles)
                                                    {
                                                        <span class="badge bg-info me-1">@role</span>
                                                    }
                                                </td>
                                                <td>
                                                    @userWithRole.User.CreatedAt.ToString("dd/MM/yyyy")
                                                    <br>
                                                    <small class="text-muted">
                                                        @{
                                                            var daysSince = (DateTime.Now - userWithRole.User.CreatedAt).Days;
                                                        }
                                                        منذ @daysSince يوم
                                                    </small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-success" 
                                                                onclick="approveUser('@userWithRole.User.Id')"
                                                                title="موافقة">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-danger" 
                                                                onclick="rejectUser('@userWithRole.User.Id', '@userWithRole.User.FullName')"
                                                                title="رفض">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-info" 
                                                                onclick="viewUserDetails('@userWithRole.User.Id')"
                                                                title="عرض التفاصيل">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </form>

                        <!-- Pagination -->
                        @if (totalPages > 1)
                        {
                            <nav class="mt-4">
                                <ul class="pagination justify-content-center">
                                    @for (int i = 1; i <= totalPages; i++)
                                    {
                                        <li class="page-item @(i == currentPage ? "active" : "")">
                                            <a class="page-link" asp-action="PendingApprovals" asp-route-page="@i">@i</a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-check-circle fa-4x text-success mb-4"></i>
                            <h5 class="text-muted">لا توجد طلبات موافقة</h5>
                            <p class="text-muted">جميع المستخدمين تم الموافقة عليهم</p>
                            <div class="mt-4">
                                <a asp-action="Index" class="btn btn-primary">
                                    <i class="fas fa-arrow-left me-2"></i>العودة للوحة التحكم
                                </a>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للرفض -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفض طلب المستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rejectForm" asp-action="RejectUser" method="post">
                <div class="modal-body">
                    <input type="hidden" id="rejectUserId" name="userId" />
                    <p>هل أنت متأكد من رفض طلب المستخدم: <strong id="rejectUserName"></strong>؟</p>
                    <div class="mb-3">
                        <label for="rejectReason" class="form-label">سبب الرفض (اختياري)</label>
                        <textarea id="rejectReason" name="reason" class="form-control" rows="3" 
                                  placeholder="اكتب سبب الرفض..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">رفض الطلب</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}
</style>

@section Scripts {
    <script>
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.user-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        function approveSelected() {
            const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
            if (selectedCheckboxes.length === 0) {
                alert('يرجى تحديد مستخدم واحد على الأقل');
                return;
            }
            
            if (confirm(`هل أنت متأكد من الموافقة على ${selectedCheckboxes.length} مستخدم؟`)) {
                document.getElementById('bulkApprovalForm').submit();
            }
        }

        function approveUser(userId) {
            if (confirm('هل أنت متأكد من الموافقة على هذا المستخدم؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '@Url.Action("ApproveUser")';
                
                const userIdInput = document.createElement('input');
                userIdInput.type = 'hidden';
                userIdInput.name = 'userId';
                userIdInput.value = userId;
                
                const tokenInput = document.createElement('input');
                tokenInput.type = 'hidden';
                tokenInput.name = '__RequestVerificationToken';
                tokenInput.value = document.querySelector('input[name="__RequestVerificationToken"]').value;
                
                form.appendChild(userIdInput);
                form.appendChild(tokenInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        function rejectUser(userId, userName) {
            document.getElementById('rejectUserId').value = userId;
            document.getElementById('rejectUserName').textContent = userName;
            new bootstrap.Modal(document.getElementById('rejectModal')).show();
        }

        function viewUserDetails(userId) {
            // يمكن إضافة modal لعرض تفاصيل المستخدم
            alert('عرض تفاصيل المستخدم - قيد التطوير');
        }
    </script>
}
