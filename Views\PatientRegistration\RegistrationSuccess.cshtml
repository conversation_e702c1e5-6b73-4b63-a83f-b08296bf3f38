@{
    ViewData["Title"] = "تم التسجيل بنجاح";
}

<section class="container py-5" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-body text-center p-5">
                    <div class="success-icon mb-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
                    </div>
                    
                    <h2 class="text-success mb-3">تم التسجيل بنجاح!</h2>
                    
                    <p class="lead text-muted mb-4">
                        شكراً لك على التسجيل في نظام HealthTrack
                    </p>
                    
                    <div class="alert alert-info" role="alert">
                        <h5 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>الخطوات التالية
                        </h5>
                        <hr>
                        <p class="mb-2">
                            <i class="fas fa-clock me-2 text-warning"></i>
                            سيتم مراجعة بياناتك من قبل فريق العمل خلال 24 ساعة
                        </p>
                        <p class="mb-2">
                            <i class="fas fa-envelope me-2 text-primary"></i>
                            ستصلك رسالة تأكيد على البريد الإلكتروني عند تفعيل حسابك
                        </p>
                        <p class="mb-0">
                            <i class="fas fa-phone me-2 text-success"></i>
                            يمكنك التواصل معنا في حالة وجود أي استفسار
                        </p>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-6 mb-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-user-check text-primary me-2"></i>
                                        حالة الحساب
                                    </h6>
                                    <p class="card-text text-warning">
                                        <i class="fas fa-hourglass-half me-1"></i>
                                        في انتظار المراجعة
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-calendar-alt text-success me-2"></i>
                                        تاريخ التسجيل
                                    </h6>
                                    <p class="card-text">
                                        @DateTime.Now.ToString("dd/MM/yyyy - HH:mm")
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <a asp-controller="Home" asp-action="Index" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-home me-2"></i>العودة للصفحة الرئيسية
                        </a>
                        <a asp-controller="Account" asp-action="Login" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    </div>
                    
                    <div class="mt-5 pt-4 border-top">
                        <h6 class="text-muted">تحتاج مساعدة؟</h6>
                        <p class="text-muted mb-2">
                            <i class="fas fa-phone me-2"></i>
                            هاتف: ************
                        </p>
                        <p class="text-muted">
                            <i class="fas fa-envelope me-2"></i>
                            البريد الإلكتروني: <EMAIL>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.success-icon {
    animation: bounceIn 1s ease-in-out;
}

@@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.card {
    border-radius: 15px;
}

.alert {
    border-radius: 10px;
}
</style>
