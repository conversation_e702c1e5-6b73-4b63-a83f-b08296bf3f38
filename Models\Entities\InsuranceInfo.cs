using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HealthTrack.Models.Entities
{
    public class InsuranceInfo
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public string PatientId { get; set; } = string.Empty;

        [ForeignKey("PatientId")]
        public virtual ApplicationUser Patient { get; set; } = null!;

        [Required]
        [StringLength(100)]
        public string InsuranceCompany { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string PolicyNumber { get; set; } = string.Empty;

        [StringLength(50)]
        public string? GroupNumber { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [StringLength(100)]
        public string? CoverageType { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? CoverageAmount { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? CoveragePercentage { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        [Required]
        public string CreatedBy { get; set; } = string.Empty;

        public string? UpdatedBy { get; set; }
    }
}
