@{
    ViewData["Title"] = "لوحة تحكم الموظف";
    var stats = ViewBag.Stats;
}

<div class="container-fluid py-4" dir="rtl">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">لوحة تحكم الموظف</h1>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المرضى
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@stats.TotalPatients.Count</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-injured fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                تسجيلات اليوم
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@stats.TodayRegistrations</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                في انتظار الموافقة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@stats.PendingApprovals</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                السجلات الطبية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@stats.TotalMedicalRecords</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-medical fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإجراءات السريعة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الإجراءات السريعة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <a asp-action="RegisterPatient" class="btn btn-primary btn-block">
                                <i class="fas fa-user-plus"></i> تسجيل مريض جديد
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a asp-action="PatientsList" class="btn btn-success btn-block">
                                <i class="fas fa-list"></i> قائمة المرضى
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a asp-action="PatientsList" class="btn btn-info btn-block">
                                <i class="fas fa-file-medical-alt"></i> السجلات الطبية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- آخر الأنشطة -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">ملاحظات مهمة</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info" role="alert">
                        <h6 class="alert-heading">تذكير!</h6>
                        <p class="mb-0">
                            • تأكد من إدخال جميع البيانات المطلوبة عند تسجيل مريض جديد<br>
                            • لا يمكن تعديل البيانات بعد الإدخال، تأكد من صحة المعلومات<br>
                            • يتم تفعيل حسابات المرضى المسجلين من قبل الموظفين تلقائياً
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-right-primary {
    border-right: 0.25rem solid #4e73df !important;
}
.border-right-success {
    border-right: 0.25rem solid #1cc88a !important;
}
.border-right-info {
    border-right: 0.25rem solid #36b9cc !important;
}
.border-right-warning {
    border-right: 0.25rem solid #f6c23e !important;
}
</style>
