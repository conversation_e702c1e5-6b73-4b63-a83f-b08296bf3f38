/* Root Variables */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --light-color: #f8fafc;
  --dark-color: #1e293b;
  --border-radius: 12px;
  --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --transition: all 0.3s ease;
}

/* Base Styles */
html {
  font-size: 16px;
  scroll-behavior: smooth;
  direction: rtl;
  text-align: right;
}

body {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--dark-color);
  background-color: #ffffff;
  margin: 0;
  padding: 0;
  direction: rtl;
  text-align: right;
}

/* Main Layout */
main {
  margin: 0;
  padding: 0;
}

header {
  margin-bottom: 0;
}

.container-fluid {
  padding: 0;
}

/* Hero Section Base */
.hero-section {
  position: relative;
  overflow: hidden;
}

.hero-section * {
  position: relative;
  z-index: 1;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Cairo', sans-serif;
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 1rem;
  text-align: right;
  direction: rtl;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  text-align: right;
  direction: rtl;
  color: #1e293b;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dark-color);
  text-align: center;
  direction: rtl;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--secondary-color);
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
  direction: rtl;
  line-height: 1.6;
}

/* Buttons */
.btn {
  border-radius: var(--border-radius);
  font-weight: 600;
  padding: 12px 24px;
  transition: var(--transition);
  border: none;
  font-family: 'Cairo', sans-serif;
  direction: rtl;
  text-align: center;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.btn-lg {
  padding: 16px 32px;
  font-size: 1.1rem;
}

/* Navigation */
.navbar {
  padding: 0.5rem 0;
  transition: var(--transition);
  margin-bottom: 0;
  direction: rtl;
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color) !important;
  font-family: 'Cairo', sans-serif;
}

.brand-text {
  color: var(--primary-color);
  font-family: 'Cairo', sans-serif;
}

.nav-link {
  font-weight: 500;
  color: var(--dark-color) !important;
  padding: 8px 16px !important;
  border-radius: 8px;
  transition: var(--transition);
  font-family: 'Cairo', sans-serif;
  direction: rtl;
}

.nav-link:hover, .nav-link.active {
  color: var(--primary-color) !important;
  background-color: rgba(37, 99, 235, 0.1);
}

/* Hero Section */
.hero-section {
  padding: 100px 0 120px 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
  margin-top: 0;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-content {
  position: relative;
  z-index: 2;
  padding: 20px 0;
}

.hero-subtitle {
  font-size: 1.3rem;
  color: #475569;
  line-height: 1.7;
  margin-bottom: 2rem;
  text-align: right;
  direction: rtl;
  font-weight: 400;
}

.min-vh-75 {
  min-height: 100vh;
  display: flex;
  align-items: center;
}

/* Hero Badge */
.hero-badge {
  margin-bottom: 20px;
  text-align: right;
  direction: rtl;
}

.hero-badge .badge {
  font-size: 0.9rem;
  border-radius: 25px;
  font-weight: 500;
  font-family: 'Cairo', sans-serif;
  padding: 8px 16px;
}

.bg-primary-light {
  background-color: rgba(37, 99, 235, 0.1) !important;
}

/* Hero Buttons */
.hero-buttons {
  margin-top: 30px;
  text-align: right;
  direction: rtl;
}

.hero-buttons .btn {
  margin: 0 10px 10px 0;
  font-size: 1rem;
  padding: 14px 28px;
}

/* Hero Stats */
.hero-stats {
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--border-radius);
  padding: 25px 20px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  margin: 30px 0;
}

.stat-number {
  font-size: 1.6rem;
  font-weight: 700;
  font-family: 'Cairo', sans-serif;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.85rem;
  color: #64748b;
  font-family: 'Cairo', sans-serif;
  margin-top: 5px;
}

/* Hero Features List */
.hero-features {
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--border-radius);
  padding: 25px 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  margin: 30px 0;
}

.feature-item {
  font-size: 0.95rem;
  color: #334155;
  font-family: 'Cairo', sans-serif;
  direction: rtl;
  text-align: right;
  font-weight: 500;
}

.feature-item i {
  font-size: 1.1rem;
  margin-left: 8px;
  color: var(--success-color);
}

/* Hero Image Container */
.hero-image-container {
  position: relative;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.hero-background-circle {
  position: absolute;
  width: 450px;
  height: 450px;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(59, 130, 246, 0.05));
  border-radius: 50%;
  z-index: 1;
  animation: float 6s ease-in-out infinite;
}

.hero-character {
  position: relative;
  z-index: 3;
  text-align: center;
  max-width: 100%;
}

.character-image {
  max-width: 400px;
  width: 100%;
  height: auto;
  filter: drop-shadow(0 25px 50px rgba(37, 99, 235, 0.2));
  animation: bounce 4s ease-in-out infinite;
  object-fit: contain;
  z-index: 4;
  position: relative;
}

/* Floating Elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  pointer-events: none;
}

.floating-card {
  position: absolute;
  background: rgba(255, 255, 255, 0.98);
  padding: 16px 20px;
  border-radius: 15px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.6);
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
  font-size: 0.9rem;
  animation: floatCard 5s ease-in-out infinite;
  font-family: 'Cairo', sans-serif;
  direction: rtl;
  white-space: nowrap;
  color: #334155;
}

.floating-card i {
  font-size: 1.3rem;
  flex-shrink: 0;
}

.floating-card.card-1 {
  top: 15%;
  right: 10%;
  animation-delay: 0s;
}

.floating-card.card-2 {
  top: 65%;
  left: 8%;
  animation-delay: 2s;
}

.floating-card.card-3 {
  bottom: 20%;
  right: 15%;
  animation-delay: 4s;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(1deg);
  }
  66% {
    transform: translateY(-25px) rotate(-1deg);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-12px) scale(1.02);
  }
}

@keyframes floatCard {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.9;
  }
  50% {
    transform: translateY(-18px) scale(1.05);
    opacity: 1;
  }
}

/* Image Placeholder */
.image-placeholder {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: var(--border-radius);
  padding: 60px 40px;
  text-align: center;
  color: white;
  box-shadow: var(--box-shadow);
}

.image-placeholder i {
  font-size: 6rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.image-placeholder p {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.8;
}

/* Features Section */
.features-section {
  padding: 100px 0;
  direction: rtl;
}

.feature-card {
  background: white;
  padding: 40px 30px;
  border-radius: var(--border-radius);
  text-align: center;
  transition: var(--transition);
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  height: 100%;
  direction: rtl;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  color: white;
  font-size: 2rem;
}

.feature-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 16px;
  font-family: 'Cairo', sans-serif;
  direction: rtl;
}

.feature-description {
  color: var(--secondary-color);
  line-height: 1.7;
  margin: 0;
  font-family: 'Cairo', sans-serif;
  direction: rtl;
}

/* Testimonials Section */
.testimonials-section {
  padding: 100px 0;
  background-color: var(--light-color);
  direction: rtl;
}

.testimonial-card {
  background: white;
  padding: 35px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  height: 100%;
  transition: var(--transition);
  direction: rtl;
  text-align: right;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.testimonial-content {
  margin-bottom: 24px;
}

.stars {
  font-size: 1.1rem;
  text-align: right;
  direction: rtl;
}

.testimonial-text {
  font-style: normal;
  color: var(--secondary-color);
  line-height: 1.7;
  margin: 0;
  font-family: 'Cairo', sans-serif;
  direction: rtl;
  text-align: right;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 16px;
  direction: rtl;
}

.author-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  order: 2;
}

.author-info {
  order: 1;
  text-align: right;
  direction: rtl;
}

.author-name {
  font-weight: 600;
  color: var(--dark-color);
  margin: 0;
  font-family: 'Cairo', sans-serif;
}

.author-location {
  color: var(--secondary-color);
  font-size: 0.9rem;
  margin: 0;
  font-family: 'Cairo', sans-serif;
}

/* CTA Section */
.cta-section {
  padding: 100px 0;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  position: relative;
  overflow: hidden;
  direction: rtl;
  text-align: center;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  font-family: 'Cairo', sans-serif;
  direction: rtl;
}

.cta-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  font-family: 'Cairo', sans-serif;
  direction: rtl;
  line-height: 1.6;
}

/* Footer */
.footer {
  margin-top: auto;
  direction: rtl;
}

.footer h5, .footer h6 {
  font-family: 'Cairo', sans-serif;
  direction: rtl;
}

.footer p, .footer li {
  font-family: 'Cairo', sans-serif;
  direction: rtl;
  text-align: right;
}

.social-links a {
  transition: var(--transition);
}

.social-links a:hover {
  color: var(--primary-color) !important;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 992px) {
  .hero-title {
    font-size: 3rem;
  }

  .hero-image-container {
    height: 400px;
    margin-top: 30px;
  }

  .character-image {
    max-width: 320px;
  }

  .hero-background-circle {
    width: 380px;
    height: 380px;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
    text-align: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .hero-section {
    padding: 60px 0 80px 0;
    min-height: 100vh;
  }

  .features-section,
  .testimonials-section,
  .cta-section {
    padding: 50px 0;
  }

  .feature-card {
    padding: 30px 20px;
    margin-bottom: 20px;
  }

  .hero-buttons {
    text-align: center;
  }

  .hero-buttons .btn {
    display: inline-block;
    width: auto;
    margin: 5px 10px;
    min-width: 150px;
  }

  /* Hero responsive adjustments */
  .hero-image-container {
    height: 350px;
    margin-top: 20px;
    padding: 10px;
  }

  .character-image {
    max-width: 280px;
  }

  .hero-background-circle {
    width: 320px;
    height: 320px;
  }

  .floating-card {
    padding: 10px 14px;
    font-size: 0.8rem;
  }

  .floating-card i {
    font-size: 1.1rem;
  }

  .hero-stats {
    padding: 15px;
    margin: 20px 0;
  }

  .stat-number {
    font-size: 1.3rem;
  }

  .hero-features {
    padding: 15px;
    margin: 20px 0;
  }

  .feature-item {
    font-size: 0.9rem;
    margin-bottom: 8px;
  }

  .hero-content {
    text-align: center;
  }

  .hero-subtitle {
    font-size: 1.1rem;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
    text-align: center;
  }

  .cta-title {
    font-size: 2rem;
  }

  .navbar-brand {
    font-size: 1.3rem;
  }

  .hero-section {
    min-height: 100vh;
    padding: 40px 0 60px 0;
  }

  .hero-subtitle {
    font-size: 1rem;
    text-align: center;
    padding: 0 10px;
  }

  .hero-buttons .btn {
    display: block;
    width: 90%;
    margin: 8px auto;
    font-size: 0.9rem;
  }

  /* Extra small screens adjustments */
  .hero-image-container {
    height: 280px;
    padding: 5px;
  }

  .character-image {
    max-width: 220px;
  }

  .hero-background-circle {
    width: 260px;
    height: 260px;
  }

  .floating-card {
    padding: 8px 12px;
    font-size: 0.75rem;
    border-radius: 10px;
  }

  .floating-card i {
    font-size: 1rem;
  }

  .floating-card.card-1 {
    top: 10%;
    right: 5%;
  }

  .floating-card.card-2 {
    top: 70%;
    left: 3%;
  }

  .floating-card.card-3 {
    bottom: 15%;
    right: 8%;
  }

  .hero-stats {
    padding: 12px;
    margin: 15px 0;
  }

  .hero-stats .row {
    text-align: center;
  }

  .stat-number {
    font-size: 1.2rem;
  }

  .stat-label {
    font-size: 0.75rem;
  }

  .hero-features {
    padding: 12px;
    margin: 15px 0;
  }

  .feature-item {
    font-size: 0.85rem;
    margin-bottom: 6px;
  }

  .container {
    padding-left: 10px;
    padding-right: 10px;
  }
}

/* Features Section Cards */
.feature-card {
  text-align: center;
  direction: rtl;
  padding: 30px 20px;
  border-radius: 15px;
  background: #ffffff;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(37, 99, 235, 0.15);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px auto;
  color: white;
  font-size: 2rem;
}

.feature-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--dark-color);
  text-align: center;
  margin-bottom: 15px;
  font-family: 'Cairo', sans-serif;
  direction: rtl;
}

.feature-description {
  font-size: 1rem;
  color: var(--secondary-color);
  line-height: 1.6;
  text-align: center;
  direction: rtl;
  font-family: 'Cairo', sans-serif;
}

/* CTA Section */
.cta-section {
  text-align: center !important;
}

.cta-title {
  text-align: center !important;
  direction: rtl;
  font-family: 'Cairo', sans-serif;
  font-size: 2.5rem;
  font-weight: 700;
}

.cta-subtitle {
  text-align: center !important;
  direction: rtl;
  font-family: 'Cairo', sans-serif;
  font-size: 1.2rem;
}

.border-primary {
  border-color: var(--primary-color) !important;
}

/* Animation Classes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Focus States */
.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* Logo Placeholder */
.logo-placeholder {
  transition: var(--transition);
}

.navbar-brand:hover .logo-placeholder {
  transform: scale(1.1);
}