using HealthTrack.Data;
using HealthTrack.Models.Entities;
using HealthTrack.Models.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HealthTrack.Controllers
{
    [Authorize(Roles = "Employee")]
    public class EmployeeController : Controller
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ApplicationDbContext _context;

        public EmployeeController(UserManager<ApplicationUser> userManager, ApplicationDbContext context)
        {
            _userManager = userManager;
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            ViewData["Title"] = "لوحة تحكم الموظف";
            
            var stats = new
            {
                TotalPatients = await _userManager.GetUsersInRoleAsync("Patient"),
                TodayRegistrations = await _userManager.Users
                    .Where(u => u.CreatedAt.Date == DateTime.Today)
                    .CountAsync(),
                PendingApprovals = await _userManager.Users
                    .Where(u => !u.IsApproved)
                    .CountAsync(),
                TotalMedicalRecords = await _context.MedicalRecords.CountAsync()
            };

            ViewBag.Stats = stats;
            return View();
        }

        [HttpGet]
        public IActionResult RegisterPatient()
        {
            ViewData["Title"] = "تسجيل مريض جديد";
            return View(new RegisterViewModel());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RegisterPatient(RegisterViewModel model)
        {
            if (!ModelState.IsValid)
            {
                ViewData["Title"] = "تسجيل مريض جديد";
                return View(model);
            }

            var existingUser = await _userManager.FindByEmailAsync(model.Email);
            if (existingUser != null)
            {
                ModelState.AddModelError("Email", "البريد الإلكتروني مستخدم بالفعل");
                ViewData["Title"] = "تسجيل مريض جديد";
                return View(model);
            }

            var existingNationalId = await _userManager.Users.FirstOrDefaultAsync(u => u.NationalId == model.NationalId);
            if (existingNationalId != null)
            {
                ModelState.AddModelError("NationalId", "رقم الهوية مستخدم بالفعل");
                ViewData["Title"] = "تسجيل مريض جديد";
                return View(model);
            }

            var user = new ApplicationUser
            {
                UserName = model.Email,
                Email = model.Email,
                FirstName = model.FirstName,
                MiddleName = model.MiddleName,
                FatherName = model.FatherName,
                LastName = model.LastName,
                NationalId = model.NationalId,
                PhoneNumber = model.PhoneNumber,
                Gender = model.Gender,
                DateOfBirth = model.DateOfBirth,
                Address = model.Address,
                MaritalStatus = model.MaritalStatus,
                IsActive = true,
                IsApproved = true, // الموظف يمكنه تفعيل المرضى مباشرة
                EmailConfirmed = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = User.Identity!.Name
            };

            var result = await _userManager.CreateAsync(user, model.Password);

            if (result.Succeeded)
            {
                await _userManager.AddToRoleAsync(user, "Patient");

                // إضافة معلومات التأمين إذا كانت متوفرة
                if (!string.IsNullOrEmpty(model.InsuranceCompany) && !string.IsNullOrEmpty(model.PolicyNumber))
                {
                    var insuranceInfo = new InsuranceInfo
                    {
                        PatientId = user.Id,
                        InsuranceCompany = model.InsuranceCompany,
                        PolicyNumber = model.PolicyNumber,
                        GroupNumber = model.GroupNumber,
                        ExpiryDate = model.InsuranceExpiryDate,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = User.Identity!.Name!
                    };

                    _context.InsuranceInfos.Add(insuranceInfo);
                    await _context.SaveChangesAsync();
                }

                TempData["SuccessMessage"] = "تم تسجيل المريض بنجاح";
                return RedirectToAction("PatientsList");
            }

            foreach (var error in result.Errors)
            {
                ModelState.AddModelError(string.Empty, error.Description);
            }

            ViewData["Title"] = "تسجيل مريض جديد";
            return View(model);
        }

        public async Task<IActionResult> PatientsList(int page = 1, int pageSize = 10)
        {
            ViewData["Title"] = "قائمة المرضى";

            var patients = await _userManager.GetUsersInRoleAsync("Patient");
            var totalPatients = patients.Count;
            
            var paginatedPatients = patients
                .OrderByDescending(p => p.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            ViewBag.Patients = paginatedPatients;
            ViewBag.CurrentPage = page;
            ViewBag.PageSize = pageSize;
            ViewBag.TotalPages = (int)Math.Ceiling((double)totalPatients / pageSize);

            return View();
        }

        [HttpGet]
        public async Task<IActionResult> CreateMedicalRecord(string patientId)
        {
            var patient = await _userManager.FindByIdAsync(patientId);
            if (patient == null)
            {
                TempData["ErrorMessage"] = "المريض غير موجود";
                return RedirectToAction("PatientsList");
            }

            ViewData["Title"] = $"إنشاء سجل طبي - {patient.FullName}";
            ViewBag.Patient = patient;
            
            var model = new MedicalRecord { PatientId = patientId };
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateMedicalRecord(MedicalRecord model)
        {
            if (!ModelState.IsValid)
            {
                var patient = await _userManager.FindByIdAsync(model.PatientId);
                ViewData["Title"] = $"إنشاء سجل طبي - {patient?.FullName}";
                ViewBag.Patient = patient;
                return View(model);
            }

            model.CreatedAt = DateTime.UtcNow;
            model.CreatedBy = User.Identity!.Name!;

            _context.MedicalRecords.Add(model);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "تم إنشاء السجل الطبي بنجاح";
            return RedirectToAction("ViewMedicalRecord", new { id = model.Id });
        }

        public async Task<IActionResult> ViewMedicalRecord(int id)
        {
            var record = await _context.MedicalRecords
                .Include(m => m.Patient)
                .Include(m => m.MedicalVisits)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (record == null)
            {
                TempData["ErrorMessage"] = "السجل الطبي غير موجود";
                return RedirectToAction("PatientsList");
            }

            ViewData["Title"] = $"السجل الطبي - {record.Patient.FullName}";
            return View(record);
        }

        [HttpGet]
        public async Task<IActionResult> AddMedicalVisit(int recordId)
        {
            var record = await _context.MedicalRecords
                .Include(m => m.Patient)
                .FirstOrDefaultAsync(m => m.Id == recordId);

            if (record == null)
            {
                TempData["ErrorMessage"] = "السجل الطبي غير موجود";
                return RedirectToAction("PatientsList");
            }

            ViewData["Title"] = $"إضافة زيارة طبية - {record.Patient.FullName}";
            ViewBag.Record = record;
            
            var model = new MedicalVisit 
            { 
                MedicalRecordId = recordId,
                VisitDate = DateTime.Now
            };
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddMedicalVisit(MedicalVisit model)
        {
            if (!ModelState.IsValid)
            {
                var record = await _context.MedicalRecords
                    .Include(m => m.Patient)
                    .FirstOrDefaultAsync(m => m.Id == model.MedicalRecordId);
                
                ViewData["Title"] = $"إضافة زيارة طبية - {record?.Patient.FullName}";
                ViewBag.Record = record;
                return View(model);
            }

            model.CreatedAt = DateTime.UtcNow;
            model.CreatedBy = User.Identity!.Name!;

            _context.MedicalVisits.Add(model);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "تم إضافة الزيارة الطبية بنجاح";
            return RedirectToAction("ViewMedicalRecord", new { id = model.MedicalRecordId });
        }
    }
}
