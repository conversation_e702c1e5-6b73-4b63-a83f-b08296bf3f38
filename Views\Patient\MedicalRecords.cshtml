@model List<HealthTrack.Models.Entities.MedicalRecord>
@{
    ViewData["Title"] = "سجلاتي الطبية";
}

<div class="container py-4" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-file-medical me-2"></i>سجلاتي الطبية
                    </h4>
                </div>
                <div class="card-body">
                    @if (Model != null && Model.Any())
                    {
                        <div class="row">
                            @foreach (var record in Model)
                            {
                                <div class="col-lg-6 mb-4">
                                    <div class="card border-left-primary h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <h6 class="card-title text-primary mb-0">
                                                    <i class="fas fa-file-medical me-2"></i>
                                                    سجل طبي #@record.Id
                                                </h6>
                                                <small class="text-muted">@record.CreatedAt.ToString("dd/MM/yyyy")</small>
                                            </div>

                                            <!-- معلومات أساسية -->
                                            @if (!string.IsNullOrEmpty(record.ChronicDiseases))
                                            {
                                                <div class="mb-2">
                                                    <strong class="text-muted">الأمراض المزمنة:</strong>
                                                    <p class="mb-1 text-truncate">@record.ChronicDiseases</p>
                                                </div>
                                            }

                                            @if (!string.IsNullOrEmpty(record.CurrentMedications))
                                            {
                                                <div class="mb-2">
                                                    <strong class="text-muted">الأدوية الحالية:</strong>
                                                    <p class="mb-1 text-truncate">@record.CurrentMedications</p>
                                                </div>
                                            }

                                            @if (!string.IsNullOrEmpty(record.Diagnosis))
                                            {
                                                <div class="mb-2">
                                                    <strong class="text-muted">التشخيص:</strong>
                                                    <p class="mb-1 text-truncate">@record.Diagnosis</p>
                                                </div>
                                            }

                                            <!-- معلومات إضافية -->
                                            <div class="row text-center mt-3">
                                                <div class="col-6">
                                                    <small class="text-muted">تم الإنشاء بواسطة:</small>
                                                    <br>
                                                    <span class="badge bg-info">@record.CreatedBy</span>
                                                </div>
                                                <div class="col-6">
                                                    @if (record.UpdatedAt.HasValue)
                                                    {
                                                        <small class="text-muted">آخر تحديث:</small>
                                                        <br>
                                                        <span class="text-muted">@record.UpdatedAt.Value.ToString("dd/MM/yyyy")</span>
                                                    }
                                                </div>
                                            </div>

                                            <!-- أزرار الإجراءات -->
                                            <div class="text-center mt-3">
                                                <a asp-action="ViewMedicalRecord" asp-route-id="@record.Id" 
                                                   class="btn btn-primary btn-sm">
                                                    <i class="fas fa-eye me-1"></i>عرض التفاصيل
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-file-medical fa-4x text-muted mb-4"></i>
                            <h5 class="text-muted">لا توجد سجلات طبية</h5>
                            <p class="text-muted">لم يتم إنشاء أي سجلات طبية لك بعد</p>
                            <div class="mt-4">
                                <a asp-action="Index" class="btn btn-primary">
                                    <i class="fas fa-arrow-left me-2"></i>العودة للوحة التحكم
                                </a>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    @if (Model != null && Model.Any())
    {
        <!-- إحصائيات سريعة -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3>@Model.Count</h3>
                        <p class="mb-0">إجمالي السجلات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3>@Model.Where(r => !string.IsNullOrEmpty(r.ChronicDiseases)).Count()</h3>
                        <p class="mb-0">سجلات بأمراض مزمنة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h3>@Model.Where(r => !string.IsNullOrEmpty(r.CurrentMedications)).Count()</h3>
                        <p class="mb-0">سجلات بأدوية حالية</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <a asp-action="Index" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i>العودة للوحة التحكم
                </a>
                <a asp-action="MedicalVisits" class="btn btn-info">
                    <i class="fas fa-calendar-alt me-2"></i>زياراتي الطبية
                </a>
            </div>
        </div>
    }
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.text-truncate {
    max-height: 3em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-in-out;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
</style>
