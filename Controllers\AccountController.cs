using HealthTrack.Models;
using HealthTrack.Models.Entities;
using HealthTrack.Models.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;

namespace HealthTrack.Controllers
{
    public class AccountController : Controller
    {
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly UserManager<ApplicationUser> _userManager;

        public AccountController(
            SignInManager<ApplicationUser> signInManager,
            UserManager<ApplicationUser> userManager)
        {
            _signInManager = signInManager;
            _userManager = userManager;
        }

        [HttpGet]
        public IActionResult Login(string? returnUrl = null)
        {
            ViewData["Title"] = "تسجيل الدخول";
            ViewData["ReturnUrl"] = returnUrl;
            return View(new LoginViewModel());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(LoginViewModel model, string? returnUrl = null)
        {
            if (!ModelState.IsValid)
            {
                ViewData["Title"] = "تسجيل الدخول";
                ViewData["ReturnUrl"] = returnUrl;
                return View(model);
            }

            var user = await _userManager.FindByEmailAsync(model.Email);
            if (user == null)
            {
                ModelState.AddModelError(string.Empty, "بيانات الدخول غير صحيحة");
                ViewData["Title"] = "تسجيل الدخول";
                ViewData["ReturnUrl"] = returnUrl;
                return View(model);
            }

            if (!user.IsActive)
            {
                ModelState.AddModelError(string.Empty, "تم تعطيل حسابك. يرجى التواصل مع الإدارة");
                ViewData["Title"] = "تسجيل الدخول";
                ViewData["ReturnUrl"] = returnUrl;
                return View(model);
            }

            var roles = await _userManager.GetRolesAsync(user);
            if (roles.Contains("Patient") && !user.IsApproved)
            {
                ModelState.AddModelError(string.Empty, "حسابك قيد المراجعة. سيتم تفعيله قريباً");
                ViewData["Title"] = "تسجيل الدخول";
                ViewData["ReturnUrl"] = returnUrl;
                return View(model);
            }

            var result = await _signInManager.PasswordSignInAsync(user, model.Password, model.RememberMe, lockoutOnFailure: true);

            if (result.Succeeded)
            {
                return await RedirectUserBasedOnRole(user, returnUrl);
            }

            if (result.IsLockedOut)
            {
                ModelState.AddModelError(string.Empty, "تم قفل حسابك مؤقتاً بسبب محاولات دخول خاطئة متكررة");
            }
            else
            {
                ModelState.AddModelError(string.Empty, "بيانات الدخول غير صحيحة");
            }

            ViewData["Title"] = "تسجيل الدخول";
            ViewData["ReturnUrl"] = returnUrl;
            return View(model);
        }

        private async Task<IActionResult> RedirectUserBasedOnRole(ApplicationUser user, string? returnUrl)
        {
            var roles = await _userManager.GetRolesAsync(user);

            if (roles.Contains("Admin"))
            {
                return Redirect(returnUrl ?? Url.Action("Index", "Admin")!);
            }
            else if (roles.Contains("Employee"))
            {
                return Redirect(returnUrl ?? Url.Action("Index", "Employee")!);
            }
            else if (roles.Contains("Patient"))
            {
                return Redirect(returnUrl ?? Url.Action("Index", "Patient")!);
            }

            return Redirect(returnUrl ?? Url.Action("Index", "Home")!);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Logout()
        {
            await _signInManager.SignOutAsync();
            return RedirectToAction("Index", "Home");
        }
    }
}