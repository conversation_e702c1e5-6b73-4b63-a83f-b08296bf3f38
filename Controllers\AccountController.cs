using HealthTrack.Models;
using Microsoft.AspNetCore.Mvc;

namespace HealthTrack.Controllers
{
    public class AccountController : Controller
    {
        [HttpGet]
        public IActionResult Login(string? returnUrl = null)
        {
            ViewData["Title"] = "تسجيل الدخول";
            ViewData["ReturnUrl"] = returnUrl;
            return View(new LoginViewModel());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult Login(LoginViewModel model, string? returnUrl = null)
        {
            if (!ModelState.IsValid)
            {
                ViewData["Title"] = "تسجيل الدخول";
                ViewData["ReturnUrl"] = returnUrl;
                return View(model);
            }

            // TODO: استبدل منطق التحقق بمنظومة الهوية الفعلية (Identity/Custom)
            if (model.Email == "<EMAIL>" && model.Password == "Password123!")
            {
                // ملاحظة: لا يوجد تكامل مع Identity هنا. فقط مثال توضيحي.
                TempData["LoginMessage"] = "تم تسجيل الدخول بنجاح (مثال تجريبي).";
                return Redirect(returnUrl ?? Url.Action("Index", "Home")!);
            }

            ModelState.AddModelError(string.Empty, "بيانات الدخول غير صحيحة");
            ViewData["Title"] = "تسجيل الدخول";
            ViewData["ReturnUrl"] = returnUrl;
            return View(model);
        }
    }
}