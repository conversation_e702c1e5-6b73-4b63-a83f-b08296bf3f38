@{
    ViewData["Title"] = "إدارة المستخدمين";
    var usersWithRoles = ViewBag.UsersWithRoles as List<(HealthTrack.Models.Entities.ApplicationUser User, IList<string> Roles)>;
    var currentRole = ViewBag.CurrentRole as string;
    var currentPage = ViewBag.CurrentPage;
    var totalPages = ViewBag.TotalPages;
}

<div class="container-fluid py-4" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">إدارة المستخدمين</h6>
                    <a asp-action="CreateEmployee" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> إضافة موظف جديد
                    </a>
                </div>
                <div class="card-body">
                    <!-- فلاتر -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="btn-group" role="group">
                                <a asp-action="ManageUsers" asp-route-role="All" 
                                   class="btn @(currentRole == "All" ? "btn-primary" : "btn-outline-primary")">
                                    الكل
                                </a>
                                <a asp-action="ManageUsers" asp-route-role="Admin" 
                                   class="btn @(currentRole == "Admin" ? "btn-primary" : "btn-outline-primary")">
                                    المديرين
                                </a>
                                <a asp-action="ManageUsers" asp-route-role="Employee" 
                                   class="btn @(currentRole == "Employee" ? "btn-primary" : "btn-outline-primary")">
                                    الموظفين
                                </a>
                                <a asp-action="ManageUsers" asp-route-role="Patient" 
                                   class="btn @(currentRole == "Patient" ? "btn-primary" : "btn-outline-primary")">
                                    المرضى
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- جدول المستخدمين -->
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>الاسم الكامل</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>رقم الهوية</th>
                                    <th>الدور</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (usersWithRoles != null && usersWithRoles.Any())
                                {
                                    @foreach (var userWithRole in usersWithRoles)
                                    {
                                        <tr>
                                            <td>@userWithRole.User.FullName</td>
                                            <td>@userWithRole.User.Email</td>
                                            <td>@userWithRole.User.NationalId</td>
                                            <td>
                                                @foreach (var role in userWithRole.Roles)
                                                {
                                                    <span class="badge bg-info me-1">@role</span>
                                                }
                                            </td>
                                            <td>
                                                @if (userWithRole.User.IsActive)
                                                {
                                                    @if (userWithRole.User.IsApproved)
                                                    {
                                                        <span class="badge bg-success">نشط</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-warning">في انتظار الموافقة</span>
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">معطل</span>
                                                }
                                            </td>
                                            <td>@userWithRole.User.CreatedAt.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    @if (!userWithRole.User.IsApproved && userWithRole.Roles.Contains("Patient"))
                                                    {
                                                        <form asp-action="ApproveUser" method="post" class="d-inline">
                                                            <input type="hidden" name="userId" value="@userWithRole.User.Id" />
                                                            <button type="submit" class="btn btn-success btn-sm" 
                                                                    onclick="return confirm('هل تريد الموافقة على هذا المستخدم؟')">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        </form>
                                                    }
                                                    
                                                    <form asp-action="ToggleUserStatus" method="post" class="d-inline">
                                                        <input type="hidden" name="userId" value="@userWithRole.User.Id" />
                                                        <button type="submit" 
                                                                class="btn @(userWithRole.User.IsActive ? "btn-warning" : "btn-success") btn-sm"
                                                                onclick="return confirm('@(userWithRole.User.IsActive ? "هل تريد تعطيل" : "هل تريد تفعيل") هذا المستخدم؟')">
                                                            <i class="fas @(userWithRole.User.IsActive ? "fa-ban" : "fa-check")"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="7" class="text-center">لا توجد بيانات</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if (totalPages > 1)
                    {
                        <nav>
                            <ul class="pagination justify-content-center">
                                @for (int i = 1; i <= totalPages; i++)
                                {
                                    <li class="page-item @(i == currentPage ? "active" : "")">
                                        <a class="page-link" asp-action="ManageUsers" asp-route-role="@currentRole" asp-route-page="@i">@i</a>
                                    </li>
                                }
                            </ul>
                        </nav>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
