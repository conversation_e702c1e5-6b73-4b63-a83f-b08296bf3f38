using HealthTrack.Data;
using HealthTrack.Models.Entities;
using HealthTrack.Models.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HealthTrack.Controllers
{
    [Authorize(Roles = "Patient")]
    public class PatientController : Controller
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ApplicationDbContext _context;

        public PatientController(UserManager<ApplicationUser> userManager, ApplicationDbContext context)
        {
            _userManager = userManager;
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            ViewData["Title"] = "لوحة تحكم المريض";
            
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var medicalRecords = await _context.MedicalRecords
                .Where(m => m.PatientId == user.Id)
                .CountAsync();

            var lastVisit = await _context.MedicalVisits
                .Include(v => v.MedicalRecord)
                .Where(v => v.MedicalRecord.PatientId == user.Id)
                .OrderByDescending(v => v.VisitDate)
                .FirstOrDefaultAsync();

            var insuranceInfo = await _context.InsuranceInfos
                .Where(i => i.PatientId == user.Id && i.IsActive)
                .FirstOrDefaultAsync();

            ViewBag.User = user;
            ViewBag.MedicalRecordsCount = medicalRecords;
            ViewBag.LastVisit = lastVisit;
            ViewBag.InsuranceInfo = insuranceInfo;

            return View();
        }

        public async Task<IActionResult> Profile()
        {
            ViewData["Title"] = "الملف الشخصي";
            
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var insuranceInfo = await _context.InsuranceInfos
                .Where(i => i.PatientId == user.Id && i.IsActive)
                .FirstOrDefaultAsync();

            ViewBag.User = user;
            ViewBag.InsuranceInfo = insuranceInfo;

            return View();
        }

        public async Task<IActionResult> MedicalRecords()
        {
            ViewData["Title"] = "سجلاتي الطبية";
            
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var records = await _context.MedicalRecords
                .Where(m => m.PatientId == user.Id)
                .OrderByDescending(m => m.CreatedAt)
                .ToListAsync();

            return View(records);
        }

        public async Task<IActionResult> ViewMedicalRecord(int id)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var record = await _context.MedicalRecords
                .Include(m => m.MedicalVisits.OrderByDescending(v => v.VisitDate))
                .FirstOrDefaultAsync(m => m.Id == id && m.PatientId == user.Id);

            if (record == null)
            {
                TempData["ErrorMessage"] = "السجل الطبي غير موجود";
                return RedirectToAction("MedicalRecords");
            }

            ViewData["Title"] = "تفاصيل السجل الطبي";
            return View(record);
        }

        public async Task<IActionResult> MedicalVisits()
        {
            ViewData["Title"] = "زياراتي الطبية";
            
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var visits = await _context.MedicalVisits
                .Include(v => v.MedicalRecord)
                .Where(v => v.MedicalRecord.PatientId == user.Id)
                .OrderByDescending(v => v.VisitDate)
                .ToListAsync();

            return View(visits);
        }

        public async Task<IActionResult> ViewMedicalVisit(int id)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var visit = await _context.MedicalVisits
                .Include(v => v.MedicalRecord)
                .FirstOrDefaultAsync(v => v.Id == id && v.MedicalRecord.PatientId == user.Id);

            if (visit == null)
            {
                TempData["ErrorMessage"] = "الزيارة الطبية غير موجودة";
                return RedirectToAction("MedicalVisits");
            }

            ViewData["Title"] = "تفاصيل الزيارة الطبية";
            return View(visit);
        }
    }

    // Controller منفصل للتسجيل العام للمرضى
    public class PatientRegistrationController : Controller
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ApplicationDbContext _context;

        public PatientRegistrationController(UserManager<ApplicationUser> userManager, ApplicationDbContext context)
        {
            _userManager = userManager;
            _context = context;
        }

        [HttpGet]
        public IActionResult Register()
        {
            ViewData["Title"] = "تسجيل مريض جديد";
            return View(new RegisterViewModel());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Register(RegisterViewModel model)
        {
            if (!ModelState.IsValid)
            {
                ViewData["Title"] = "تسجيل مريض جديد";
                return View(model);
            }

            var existingUser = await _userManager.FindByEmailAsync(model.Email);
            if (existingUser != null)
            {
                ModelState.AddModelError("Email", "البريد الإلكتروني مستخدم بالفعل");
                ViewData["Title"] = "تسجيل مريض جديد";
                return View(model);
            }

            var existingNationalId = await _userManager.Users.FirstOrDefaultAsync(u => u.NationalId == model.NationalId);
            if (existingNationalId != null)
            {
                ModelState.AddModelError("NationalId", "رقم الهوية مستخدم بالفعل");
                ViewData["Title"] = "تسجيل مريض جديد";
                return View(model);
            }

            var user = new ApplicationUser
            {
                UserName = model.Email,
                Email = model.Email,
                FirstName = model.FirstName,
                MiddleName = model.MiddleName,
                FatherName = model.FatherName,
                LastName = model.LastName,
                NationalId = model.NationalId,
                PhoneNumber = model.PhoneNumber,
                Gender = model.Gender,
                DateOfBirth = model.DateOfBirth,
                Address = model.Address,
                MaritalStatus = model.MaritalStatus,
                IsActive = true,
                IsApproved = false, // يحتاج موافقة من الموظف
                EmailConfirmed = false,
                CreatedAt = DateTime.UtcNow
            };

            var result = await _userManager.CreateAsync(user, model.Password);

            if (result.Succeeded)
            {
                await _userManager.AddToRoleAsync(user, "Patient");

                // إضافة معلومات التأمين إذا كانت متوفرة
                if (!string.IsNullOrEmpty(model.InsuranceCompany) && !string.IsNullOrEmpty(model.PolicyNumber))
                {
                    var insuranceInfo = new InsuranceInfo
                    {
                        PatientId = user.Id,
                        InsuranceCompany = model.InsuranceCompany,
                        PolicyNumber = model.PolicyNumber,
                        GroupNumber = model.GroupNumber,
                        ExpiryDate = model.InsuranceExpiryDate,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = user.Email
                    };

                    _context.InsuranceInfos.Add(insuranceInfo);
                    await _context.SaveChangesAsync();
                }

                TempData["SuccessMessage"] = "تم تسجيلك بنجاح. سيتم مراجعة بياناتك وتفعيل حسابك قريباً";
                return RedirectToAction("RegistrationSuccess");
            }

            foreach (var error in result.Errors)
            {
                ModelState.AddModelError(string.Empty, error.Description);
            }

            ViewData["Title"] = "تسجيل مريض جديد";
            return View(model);
        }

        public IActionResult RegistrationSuccess()
        {
            ViewData["Title"] = "تم التسجيل بنجاح";
            return View();
        }
    }
}
