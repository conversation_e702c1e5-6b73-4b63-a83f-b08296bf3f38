using HealthTrack.Models.Entities;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace HealthTrack.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<MedicalRecord> MedicalRecords { get; set; }
        public DbSet<MedicalVisit> MedicalVisits { get; set; }
        public DbSet<InsuranceInfo> InsuranceInfos { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure ApplicationUser
            builder.Entity<ApplicationUser>(entity =>
            {
                entity.HasIndex(e => e.NationalId).IsUnique();
                entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.NationalId).IsRequired().HasMaxLength(20);
            });

            // Configure MedicalRecord
            builder.Entity<MedicalRecord>(entity =>
            {
                entity.HasOne(d => d.Patient)
                    .WithMany(p => p.MedicalRecords)
                    .HasForeignKey(d => d.PatientId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure MedicalVisit
            builder.Entity<MedicalVisit>(entity =>
            {
                entity.HasOne(d => d.MedicalRecord)
                    .WithMany(p => p.MedicalVisits)
                    .HasForeignKey(d => d.MedicalRecordId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure InsuranceInfo
            builder.Entity<InsuranceInfo>(entity =>
            {
                entity.HasOne(d => d.Patient)
                    .WithMany(p => p.InsuranceInfos)
                    .HasForeignKey(d => d.PatientId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }
    }
}
