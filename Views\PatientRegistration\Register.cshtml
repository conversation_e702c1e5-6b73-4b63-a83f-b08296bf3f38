@model HealthTrack.Models.ViewModels.RegisterViewModel
@{
    ViewData["Title"] = "تسجيل مريض جديد";
}

<section class="container py-5" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white text-center">
                    <h3 class="mb-0">تسجيل مريض جديد</h3>
                    <p class="mb-0">يرجى ملء جميع البيانات المطلوبة بدقة</p>
                </div>
                <div class="card-body p-4">
                    <form asp-action="Register" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <!-- البيانات الشخصية -->
                        <div class="section-header mb-3">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-user me-2"></i>البيانات الشخصية
                            </h5>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label asp-for="FirstName" class="form-label required"></label>
                                <input asp-for="FirstName" class="form-control" placeholder="الاسم الأول" />
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="MiddleName" class="form-label"></label>
                                <input asp-for="MiddleName" class="form-control" placeholder="الاسم الأوسط" />
                                <span asp-validation-for="MiddleName" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="FatherName" class="form-label"></label>
                                <input asp-for="FatherName" class="form-control" placeholder="اسم الأب" />
                                <span asp-validation-for="FatherName" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="LastName" class="form-label required"></label>
                                <input asp-for="LastName" class="form-control" placeholder="اسم العائلة" />
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label asp-for="NationalId" class="form-label required"></label>
                                <input asp-for="NationalId" class="form-control" placeholder="رقم الهوية" />
                                <span asp-validation-for="NationalId" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="Gender" class="form-label required"></label>
                                <select asp-for="Gender" class="form-select">
                                    <option value="">اختر الجنس</option>
                                    <option value="1">ذكر</option>
                                    <option value="2">أنثى</option>
                                </select>
                                <span asp-validation-for="Gender" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="DateOfBirth" class="form-label required"></label>
                                <input asp-for="DateOfBirth" class="form-control" type="date" />
                                <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="MaritalStatus" class="form-label"></label>
                                <select asp-for="MaritalStatus" class="form-select">
                                    <option value="">اختر الحالة الاجتماعية</option>
                                    <option value="1">أعزب</option>
                                    <option value="2">متزوج</option>
                                    <option value="3">مطلق</option>
                                    <option value="4">أرمل</option>
                                </select>
                                <span asp-validation-for="MaritalStatus" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="PhoneNumber" class="form-label required"></label>
                                <input asp-for="PhoneNumber" class="form-control" placeholder="رقم الهاتف" />
                                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label"></label>
                            <textarea asp-for="Address" class="form-control" rows="3" placeholder="العنوان التفصيلي"></textarea>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <!-- بيانات الحساب -->
                        <div class="section-header mb-3 mt-4">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-lock me-2"></i>بيانات الحساب
                            </h5>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label asp-for="Email" class="form-label required"></label>
                                <input asp-for="Email" class="form-control" type="email" placeholder="البريد الإلكتروني" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="Password" class="form-label required"></label>
                                <input asp-for="Password" class="form-control" type="password" placeholder="كلمة المرور" />
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="ConfirmPassword" class="form-label required"></label>
                                <input asp-for="ConfirmPassword" class="form-control" type="password" placeholder="تأكيد كلمة المرور" />
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- بيانات التأمين -->
                        <div class="section-header mb-3 mt-4">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-shield-alt me-2"></i>بيانات التأمين (اختيارية)
                            </h5>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label asp-for="InsuranceCompany" class="form-label"></label>
                                <input asp-for="InsuranceCompany" class="form-control" placeholder="شركة التأمين" />
                                <span asp-validation-for="InsuranceCompany" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="PolicyNumber" class="form-label"></label>
                                <input asp-for="PolicyNumber" class="form-control" placeholder="رقم البوليصة" />
                                <span asp-validation-for="PolicyNumber" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="GroupNumber" class="form-label"></label>
                                <input asp-for="GroupNumber" class="form-control" placeholder="رقم المجموعة" />
                                <span asp-validation-for="GroupNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="InsuranceExpiryDate" class="form-label"></label>
                                <input asp-for="InsuranceExpiryDate" class="form-control" type="date" />
                                <span asp-validation-for="InsuranceExpiryDate" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- ملاحظة مهمة -->
                        <div class="alert alert-info mt-4" role="alert">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>ملاحظة مهمة
                            </h6>
                            <p class="mb-0">
                                سيتم مراجعة بياناتك من قبل فريق العمل وتفعيل حسابك خلال 24 ساعة من التسجيل.
                                تأكد من صحة جميع البيانات المدخلة.
                            </p>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a asp-controller="Home" asp-action="Index" class="btn btn-secondary me-md-2">
                                <i class="fas fa-arrow-left me-2"></i>العودة للصفحة الرئيسية
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>تسجيل الحساب
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.required::after {
    content: " *";
    color: red;
}

.section-header h5 {
    position: relative;
}

.section-header h5::before {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, #007bff, transparent);
}
</style>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
