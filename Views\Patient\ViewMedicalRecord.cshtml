@model HealthTrack.Models.Entities.MedicalRecord
@{
    ViewData["Title"] = "تفاصيل السجل الطبي";
}

<div class="container py-4" dir="rtl">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-file-medical me-2"></i>
                            السجل الطبي #@Model.Id
                        </h4>
                        <div>
                            <span class="badge bg-light text-dark">
                                تاريخ الإنشاء: @Model.CreatedAt.ToString("dd/MM/yyyy")
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التاريخ المرضي -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-heartbeat me-2"></i>الأمراض المزمنة
                            </h6>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(Model.ChronicDiseases))
                            {
                                <p>@Model.ChronicDiseases</p>
                            }
                            else
                            {
                                <p class="text-muted">لا توجد أمراض مزمنة مسجلة</p>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header bg-warning text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>الحساسية
                            </h6>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(Model.Allergies))
                            {
                                <p>@Model.Allergies</p>
                            }
                            else
                            {
                                <p class="text-muted">لا توجد حساسية مسجلة</p>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- الأدوية والعمليات -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-pills me-2"></i>الأدوية الحالية
                            </h6>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(Model.CurrentMedications))
                            {
                                <p>@Model.CurrentMedications</p>
                            }
                            else
                            {
                                <p class="text-muted">لا توجد أدوية حالية</p>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-cut me-2"></i>العمليات السابقة
                            </h6>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(Model.PreviousSurgeries))
                            {
                                <p>@Model.PreviousSurgeries</p>
                            }
                            else
                            {
                                <p class="text-muted">لا توجد عمليات سابقة</p>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- التاريخ المرضي التفصيلي -->
            @if (!string.IsNullOrEmpty(Model.MedicalHistory))
            {
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-history me-2"></i>التاريخ المرضي التفصيلي
                        </h6>
                    </div>
                    <div class="card-body">
                        <p>@Model.MedicalHistory</p>
                    </div>
                </div>
            }

            <!-- الفحوصات والتشخيص -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-stethoscope me-2"></i>الفحوصات السريرية
                            </h6>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(Model.Examinations))
                            {
                                <p>@Model.Examinations</p>
                            }
                            else
                            {
                                <p class="text-muted">لا توجد فحوصات مسجلة</p>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header bg-dark text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-vial me-2"></i>نتائج التحاليل
                            </h6>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(Model.LabResults))
                            {
                                <p>@Model.LabResults</p>
                            }
                            else
                            {
                                <p class="text-muted">لا توجد نتائج تحاليل</p>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- التشخيص وخطة العلاج -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-diagnoses me-2"></i>التشخيص
                            </h6>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(Model.Diagnosis))
                            {
                                <p>@Model.Diagnosis</p>
                            }
                            else
                            {
                                <p class="text-muted">لا يوجد تشخيص مسجل</p>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-prescription me-2"></i>خطة العلاج
                            </h6>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(Model.TreatmentPlan))
                            {
                                <p>@Model.TreatmentPlan</p>
                            }
                            else
                            {
                                <p class="text-muted">لا توجد خطة علاج مسجلة</p>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملاحظات إضافية -->
            @if (!string.IsNullOrEmpty(Model.Notes))
            {
                <div class="card mb-4">
                    <div class="card-header bg-warning text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-sticky-note me-2"></i>ملاحظات إضافية
                        </h6>
                    </div>
                    <div class="card-body">
                        <p>@Model.Notes</p>
                    </div>
                </div>
            }

            <!-- الزيارات الطبية المرتبطة -->
            @if (Model.MedicalVisits != null && Model.MedicalVisits.Any())
            {
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>الزيارات الطبية المرتبطة (@Model.MedicalVisits.Count)
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>تاريخ الزيارة</th>
                                        <th>الشكوى الرئيسية</th>
                                        <th>التشخيص</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var visit in Model.MedicalVisits)
                                    {
                                        <tr>
                                            <td>@visit.VisitDate.ToString("dd/MM/yyyy")</td>
                                            <td>@(visit.ChiefComplaint ?? "غير محدد")</td>
                                            <td>@(visit.Diagnosis ?? "غير محدد")</td>
                                            <td>
                                                <a asp-action="ViewMedicalVisit" asp-route-id="@visit.Id" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    عرض التفاصيل
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }

            <!-- معلومات الإنشاء والتحديث -->
            <div class="card">
                <div class="card-header bg-light">
                    <h6 class="mb-0 text-muted">
                        <i class="fas fa-info-circle me-2"></i>معلومات السجل
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>تم الإنشاء بواسطة:</strong> @Model.CreatedBy</p>
                            <p><strong>تاريخ الإنشاء:</strong> @Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</p>
                        </div>
                        <div class="col-md-6">
                            @if (Model.UpdatedAt.HasValue)
                            {
                                <p><strong>آخر تحديث بواسطة:</strong> @Model.UpdatedBy</p>
                                <p><strong>تاريخ آخر تحديث:</strong> @Model.UpdatedAt.Value.ToString("dd/MM/yyyy HH:mm")</p>
                            }
                            else
                            {
                                <p class="text-muted">لم يتم تحديث السجل</p>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="text-center mt-4">
                <a asp-action="MedicalRecords" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i>العودة للسجلات
                </a>
                <a asp-action="MedicalVisits" class="btn btn-info me-2">
                    <i class="fas fa-calendar-alt me-2"></i>زياراتي الطبية
                </a>
                <a asp-action="Index" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i>لوحة التحكم
                </a>
            </div>
        </div>
    </div>
</div>
