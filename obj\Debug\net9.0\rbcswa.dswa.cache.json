{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["oF7dJrL6NE53NbzaVgI34Du4tcIsGMuTGUTet0VchFQ=", "LWIA+2XfUnkGfiEoKe9KTRlJorj0iUmLE3gnuS+vyfo=", "xQjQL3ApQet36FYWzrn62wv2UPGqIjewHjaha23vBTQ=", "xi130j2LMFGmjQBIB3Lg3sMflUMOgIdr9g9Jfe+Ybac=", "tDRl3BddYCm00Vz84/GKOzC5rjbxWPFBDmPLYCDGG5k=", "og8ZRwefqMD8tlmc/yScFYhGG5eQmHXkPt701+w/eOI=", "8UrRPhswp3x6AzHRYIpYGoJHZNt1mHDxRhXV7qCwqWQ=", "LK1GrrG4qpo4yYj2ID/qcJJmtivYh4t1WlN4HqI4jqA=", "rYxNTcYUTtrIynSAgxRl4bzVMnAD52aPxNCFAkhQZVM=", "HqSZK9v/enElUbscvI+Pd0GCvBCROJFSUDmfjltxvDY=", "gI15KBGYmCmkgrzlcBOFjDYLRieeTOc9RgvUM9rHFUo=", "KqLbBmK6Ek9dnZ33MkwLugteEH93Uo/iIZP1IXv4ZoQ=", "9IoazI46mhnJREFR1kxNT8pck1fi5jYvKdLZtClXlUs=", "2WhxEh3MZvmQTwqF7eoB/RfcbctXsIYqA9JR0FuKnsg=", "CQSJhy3cKe3XUd/i4M+wFJKE15kgd1PIo75TGD4fbJU=", "I68iIco35/vzdqdsjZ5tX34YfV5mBhC7zqQCaals8nc=", "px4oObUCEyb7RKQjfMwUUAB5bSBjTVy4xVSQZJ05e7k=", "k7C+fQspC4daBbpnaeoVX719ODwMFSRgqXrRWxatLHM=", "6dsJV436InviulBJm4djEHu0ajqoAaqqsvQhytWikiY=", "f+KNysSrsvdOF73ibQWgwNyRlOMQEkcc+TLZ+KFzPdo=", "YC1vTZGZfVqR1kM+PSabgTE3GXLPTwk8nxJBcptmanE=", "cxP48f7a5buiQ970uCV+LR9X3ZlsU5nKBL5o81gTp/8=", "FbJo8wdSE9KvbkfAHKTRvu4yB2NknaH4As03jfNMHUY=", "nRPNxhBe20a77aa5RfHWGbw5AbTNEwl7jQY1eTZishQ=", "EgAk2xeuuQHkW3lxJH/y/FNI6LUYk37920M2Gg6HQMY=", "A5UoW99qPGtPx7gsLJon6F0vaCKsruzqvssotGTzNbU=", "ru2lAgZLiYifdJpPFh1YDQbRdgDaq1rUF0Bu+6NUT68=", "8nWo7LHnHIz3EboMmClK4+Vry+8k+5p+W1O9s7YRtO0=", "GGgadbD1dtdAhSbRYZQ9bvjZuy5GOtxiOM8bxlpPrjQ=", "oDLszwxUMYHggG8ua0oCZ5PCsjN/hGT2yJFjXzHxjSI=", "GbIXEacn/z1OMAxzE+cM3YZQiNYoMxsExQkW+bwS4w0=", "FKh47YFLl3HFP0QiPLGPBwbwwFa8rhpU14tbhcHrtWc=", "6Q0bFM/ZSExkUav7DkiwhgCO6ARAp4vRm/TLJpf2uig=", "YHUwzaXcwGL3I7mIlYSgg8IXgFw7TqwzQNLNgsn+OxQ=", "3wJCqFYIsqvvDBbe4YYGwbWm9G+9AdrKSSMmixxyEFY=", "+KbgxMc/8XhMVcy58LSeoYMBFdjRPjU+G3cMOkQG+I0=", "9DnjL7jbg5RC8WxGRzsCxPjwlsvGdQ5mBbJu6n5mHyg=", "JU/ldRnk93X+mmDMEagIkvWaEdImCcR7YQ4lPmjOdZU=", "BnJRuJH0VZb6dF1GIThTUkRTwJhKrqD+Wc1OC3MLueM=", "hco1cMVlkPe7qNhvwFf5DbJAa+rqqs7gE6MHM3R8vbQ=", "pCQCPBqlIK9+wozoc0VbnIZtiwWkISdtsC0iznDFGo0=", "09Elg1WHwxk+XhCzZi84IsM0H/NjK/tYiqwJKgHKhm8=", "2BG7ivGIFQvvWerzBDFw6k4W7BLdBBMnm3XG8Y4Kwbw=", "eaHnal5sDJv8Z4FIeWLR1wH7OPcBz0Ctp4C8Oh0UuVQ=", "3eQvJ0acGgXMXKGt+ChL7KRQ2i7z10RSIkILspnzV4I=", "NqZYbSPl90h2MMtI6t9z5cmMtPTNMwTkK9ZA6rrAXkY=", "kHi/mAHG9I/eUf6pgi0rV35q09PkcTV3diD0qYDd5i4=", "SvDBWvq5axwhDHmbDoWN7yVmno9qzPDNxFb6DhnsyKU=", "nxocVea7w/IwetyT1M61jtqpLrpgjij5U31IzeHKhA0=", "wjIjD4WLz5qgdesj5jH6IxdnbyfgyFjzP5gl5mc6Gm4=", "PiUzPONeJ4JLOwWlnbha+ft/M6RSA09TmfYXrVvMhIA=", "ewQnCKxtcsW9Z2gOufm70oCZ326J6R4gMA8coNG/Lcg=", "MGamQQ9sD1ACeDZ6wnyc4G0AXJETbWg285wNNwZy8AM=", "DMt6kkRFKIcKbhSVkmLkr2MGUr7B/DqwYRqItVDQ3qA=", "ePLQiq/MXvUfqBPMEaAME0Omt9hQrSQJspG1X7gtsdE=", "u8ySzkjSMLvCkI5sSfXSV15df2tBBzVYV5lNxrXHMjc=", "8DOwlQhebPDGJa17CD8EPeCs1hzoPrLHSYMA2pbgGfA=", "E1NYCZFouwYm9Ztmgzw6GlPcUNJ6p4UXEWMMGn0p4z0=", "FW1Ttts1+vhdsF15LuWSQiHNBbWuv14GCGXlTqbtyYI=", "phXvrzUehfUjM8wObaiqy3G6AXPMnS89H6H8yx+R6jQ=", "blal3NtH+XpHUPD16C+IlcLStRKK5cFMDEiKUQECrhU=", "xGPQbO45CGSvRO8qAJV6mgHJA/Mx/hLYR5b5vJMCG6Y=", "UzUo49/8zamP8FIxu8RbE6AX3LFkaakUtOafUBlbBdI=", "vu4WLlGEcozmLwZcFKXsaSxfktnGxQYYt+7QQbd79Z8=", "YFdBu63c1xxy+uPYjqCytyvM9tKXgY7exP9Pox/urDs=", "FlQYjhllxRF58sHcSEEXS62KSgkYuiAJU4/6jlu0aUw=", "IANogeaGddYc9sElnqe1al/vhH+yt4D3q54d87KU398=", "192wEL4CtNjG2g0w0H8iH7mDs+haqBzZBT16tDQXylc=", "CNDwMyAb8ax3GujZ2SH2IltxrBEq4F0Hmdf3Q0/7uhY=", "THuStJTnNNj6xdbeOlI1m1yRyftEa/uaIxgzX3ndQpE=", "IncuzV614n8kopBH1EswHls4iqQ/7sHOjGXkeUoDAH8=", "ULrkn5In+fn5p1dnAgsY+zLZpR96SfM5OD+zPR9gWhE=", "FTOHMoJpeuk2NB7lgjOaJrITxnRWq3zdB2zXi2Bgcsc=", "ktunz2VmBUqDI3d3kFseRK6Rv1og5UAAP+Zh0Eny4Vg=", "HbFiQC4ewyjS1RvWTny+9aw6yoX65Ka9HcbKTu9d3J8=", "RfTED3lWD/nwFOowzGfPt7DEn5Fn8UDNmfn1EcJWanc=", "CZPKwI/voDUeFD5Mae3EXAkeI9v2vc+JtKnQcajdShI=", "siP06mP3YGBVDWcczKK0tOeEwI+zJdR/WfkOGFLtPJ8=", "5bea+bMRtIcGZhIFxWdj3vX2bXrKRUUfaEKZwenUL8s=", "8com/ykd5jjCSxAe+IGpZu2AkfEiH48bxy4+E6XF+QU=", "bHovHeOqkj5xdj+gtyvgh9gmoqV/vKAwdLydiouJaNk=", "TvoFj2OilzwE5lmxnlCqonbtIGX/CjSoezdbYH5OewQ=", "F1AyL/3P0cM29i14cs1/UyfTVdcRUu7YA/qvYlJASuM=", "KVws80sSwThsRvzxmEl0EPTIURD2qIxr4Pa0boutkoE=", "bwlsCH3fu9VGp1ifUpojt2pIyZojXNqAYLgMTjcnuPc=", "JeMUG4hgwBqb9LJR7jwGt3pi/qDbslQCwBM1nU6gyvs=", "zNA9z8j68v/GLPy6voKt9kDOHYOyHXJ67ediao4gTho=", "FTUDgUPu0ecT6Nk5ogGQAOyYCnO2vYMpxihD3mhXxRM=", "d7JA79nFFkNPBLCj+oUQUWOu5GIIVYGIQ6lUn4+A6yI=", "WvboPssjeZl8dUEPEXxmk1Rp5UzZjrxa9ksNkWsb65U=", "KuyNSMv3KXk4U7QKrzaD7edk3YXxUc1zdG3oBCxWBB8=", "9BANXqaK+8y6SL3/kbr8qlrzYpLUG5Qz0DNzJBp5YJY=", "FPMjiwnEbXNWhVSdaJ9jcI2oBCO5XMYf7ltHuvsOIYg=", "YLk/BzGg9/L1lKle70OaJns5dRKnfiboHDeXXnRhKcM=", "KoXk0u2VSXUzNoNPPhce9EV98ryBPNqqP//hlzDTvxs=", "A28cLQ+9e3P2Ja7nF2MV6HkfEEHxXe68EKwYS2hHnQw=", "DRX17Jz5Ff6CIX2nvnOhkSRzV5nT8AU5BPAhAfAgUvA=", "0zNU+t25/A0V72cTiia1JZdzohbQ3144vsGhc3UU/4E=", "4R1d0ME1oVVWzTDBkKKJSEsauBS0lcbZx4PTbUeTYZw=", "qH0kf9149T5xj6sE9fdPX7SY0i7zt80Ylgw4wBrM+fg=", "UNtgfFpk5E0UF1oGQi1rPCUOgl/s9OltPZwo85fQ8Kk=", "UN93HyAbWVnVknvpga1yURHRceKIEtWB1kd0MjkeR/U=", "V/VrohiTHhVMIDYLcFekWKO19RvTFtt/LifW8Jw3Z6c=", "lkdYtu5AlgOzIDcL+D3HXCZeLEJZ6ednXF8wGe5NXFs=", "8uKQ8JcC8+IhMyGinIm6+vBcTTV5rae3MdNxgFPiH2Y=", "zEeIXZhyzEjQrmMSqubEAADm6T8jLuivbWcMU7b1BuM=", "4eOrnGnsGtc8Hie1C0UeWiXUlsack3i1IS4k7hS7dDo=", "eF7GpIOSlPa8u99sbhCaG4SJNI4M3oOGZuYSKbsYMCI=", "MyuRSRZkjPT7elignjGbyKlRj3VgOr0dUoEnddu74Jw=", "G62S9aPb6qTRRu7VdM6HlBqQlyUYnj36rpg7O3borzo=", "whSD9LBLyIgWf9OikSVIGEPRrS4WpwwklEgVGqvkKqQ=", "uqACf/ZvJrsRl8ONPyZjPGXEbEP2LhJYwncZA4o/hsc=", "Qt9nayHvrrUYFqZCuRH/TAYQEfIt4EpBMd+5SxSSCGM=", "C4glN6bTw1nSlBAlDOg0G13CYoinN4F0/fl8m99fF24=", "Lw77c+gIeBYdFT3Ju/IecJETR4j946M/yMsZ4L8AAxU=", "+3CEEpBcqdw7ZiUyHe63ydcOApDPBmVjtB4MjRey0e4=", "PYMctFnTRxsGn+k60yKBcCxd3jUMR8DGWVQEz1nJ1VE=", "E8MFLOxhmUTfDcQUQpBzwrdQLQDJZ4yDxdhY55Q52Ww=", "eWCJlPYlFH/N2n4xQmxyoL4UBaTN3toMmuRsQMncrZc=", "PG/rOUDUkszYEcL6QDq9G2ddN+lFGatw6+RFmv1xKE8=", "n1e7c0aRSRt4nZGPESJ3H1adxDfuw2pbx7k/PUzoDqw=", "ODy6l+KVMeNWVbyJalHxRldvJ2aIG63pbpXgu5/ELXA=", "bUkBgVmCdS9cyq18Mv+bI2DwA1xQpwB4EUuZ97nj7yE=", "Mmu044+Ggz7Ph3ZFERM3G05rfV10PrtpfRybice7pVI=", "uVFxNzpqloS38N/jPTVsUIHLQ63ey4p9Py8lVvOVNaM=", "1tmjnQb+MVSb0/gdGd8xQEcwA3uzoWnqQ/janGCTXC0="], "CachedAssets": {"+3CEEpBcqdw7ZiUyHe63ydcOApDPBmVjtB4MjRey0e4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\095chq0xao-x0q3zqp4vz.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-08-11T10:06:34.1807862+00:00"}, "bUkBgVmCdS9cyq18Mv+bI2DwA1xQpwB4EUuZ97nj7yE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\c6ptl8ucl9-mlv21k5csn.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-08-11T10:06:34.1559258+00:00"}, "ODy6l+KVMeNWVbyJalHxRldvJ2aIG63pbpXgu5/ELXA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\9yd6thb4l9-87fc7y1x7t.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-08-11T10:06:34.1799756+00:00"}, "PYMctFnTRxsGn+k60yKBcCxd3jUMR8DGWVQEz1nJ1VE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\awxvykvflr-0i3buxo5is.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-08-11T10:06:34.1842624+00:00"}, "PG/rOUDUkszYEcL6QDq9G2ddN+lFGatw6+RFmv1xKE8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\b4j78s1ol2-2z0ns9nrw6.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-08-11T10:06:34.190322+00:00"}, "1tmjnQb+MVSb0/gdGd8xQEcwA3uzoWnqQ/janGCTXC0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\8m9gcaymu7-6g5oj6kvn9.gz", "SourceId": "HealthTrack", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "HealthTrack#[.{fingerprint=6g5oj6kvn9}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\HealthTrack.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j1zewzlpo2", "Integrity": "GTs8kpYsYzKZ1J+UTHZDGI308djNdwNcmWzg92zdR6Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\HealthTrack.bundle.scp.css", "FileLength": 542, "LastWriteTime": "2025-08-11T10:06:34.1620799+00:00"}, "uVFxNzpqloS38N/jPTVsUIHLQ63ey4p9Py8lVvOVNaM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\hh6rz1azrz-6g5oj6kvn9.gz", "SourceId": "HealthTrack", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "HealthTrack#[.{fingerprint=6g5oj6kvn9}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\scopedcss\\bundle\\HealthTrack.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j1zewzlpo2", "Integrity": "GTs8kpYsYzKZ1J+UTHZDGI308djNdwNcmWzg92zdR6Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\scopedcss\\bundle\\HealthTrack.styles.css", "FileLength": 542, "LastWriteTime": "2025-08-11T10:06:34.1605723+00:00"}, "n1e7c0aRSRt4nZGPESJ3H1adxDfuw2pbx7k/PUzoDqw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\d7mqztheys-muycvpuwrr.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-08-11T10:06:34.1594929+00:00"}, "Mmu044+Ggz7Ph3ZFERM3G05rfV10PrtpfRybice7pVI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\q3i4l3ugj3-r1aki41k4s.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "css/site#[.{fingerprint=r1aki41k4s}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6f793fpfh8", "Integrity": "0lwS5mN2Q/h8PeNs9hkweA4cgjn1KZyhBKh+dwpvjro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\css\\site.css", "FileLength": 3509, "LastWriteTime": "2025-08-11T18:32:09.1525336+00:00"}, "UzUo49/8zamP8FIxu8RbE6AX3LFkaakUtOafUBlbBdI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\rvghe3p6z1-61n19gt1b8.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-08-11T10:06:34.190322+00:00"}, "vu4WLlGEcozmLwZcFKXsaSxfktnGxQYYt+7QQbd79Z8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\rqnvvatrvf-xtxxf3hu2r.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-08-11T10:06:34.1873206+00:00"}, "YFdBu63c1xxy+uPYjqCytyvM9tKXgY7exP9Pox/urDs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\4ipyayqy8h-bqjiyaj88i.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-08-11T10:06:34.2014669+00:00"}, "FlQYjhllxRF58sHcSEEXS62KSgkYuiAJU4/6jlu0aUw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\4qf12f94f2-c2jlpeoesf.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-08-11T10:06:34.1944711+00:00"}, "IANogeaGddYc9sElnqe1al/vhH+yt4D3q54d87KU398=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\qqylkh41a8-erw9l3u2r3.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-08-11T10:06:34.1648378+00:00"}, "192wEL4CtNjG2g0w0H8iH7mDs+haqBzZBT16tDQXylc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\woq4dz0xua-aexeepp0ev.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-08-11T10:06:34.193469+00:00"}, "CNDwMyAb8ax3GujZ2SH2IltxrBEq4F0Hmdf3Q0/7uhY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\0y9i614yx0-d7shbmvgxk.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-08-11T10:06:34.1873206+00:00"}, "THuStJTnNNj6xdbeOlI1m1yRyftEa/uaIxgzX3ndQpE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\ryyztizykj-ausgxo2sd3.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-08-11T10:06:34.1944711+00:00"}, "IncuzV614n8kopBH1EswHls4iqQ/7sHOjGXkeUoDAH8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\c0saxi0s91-k8d9w2qqmf.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-08-11T10:06:34.1828503+00:00"}, "ULrkn5In+fn5p1dnAgsY+zLZpR96SfM5OD+zPR9gWhE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\1v3s5l7wgh-cosvhxvwiu.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-08-11T10:06:34.1741985+00:00"}, "FTOHMoJpeuk2NB7lgjOaJrITxnRWq3zdB2zXi2Bgcsc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\7rrhbhrzll-ub07r2b239.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-08-11T10:06:34.1828503+00:00"}, "ktunz2VmBUqDI3d3kFseRK6Rv1og5UAAP+Zh0Eny4Vg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\6xtl83wvsb-fvhpjtyr6v.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-08-11T10:06:34.1784629+00:00"}, "HbFiQC4ewyjS1RvWTny+9aw6yoX65Ka9HcbKTu9d3J8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\gnwqkf1i4n-b7pk76d08c.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-08-11T10:06:34.1697854+00:00"}, "RfTED3lWD/nwFOowzGfPt7DEn5Fn8UDNmfn1EcJWanc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\v6viktehjb-fsbi9cje9m.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-08-11T10:06:34.1687852+00:00"}, "CZPKwI/voDUeFD5Mae3EXAkeI9v2vc+JtKnQcajdShI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\55wl1xc63l-rzd6atqjts.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-08-11T10:06:34.1620799+00:00"}, "siP06mP3YGBVDWcczKK0tOeEwI+zJdR/WfkOGFLtPJ8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\mn48rd8flt-ee0r1s7dh0.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-08-11T10:06:34.1842624+00:00"}, "5bea+bMRtIcGZhIFxWdj3vX2bXrKRUUfaEKZwenUL8s=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\0fzdp0mmbq-dxx9fxp4il.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-08-11T10:06:34.1774439+00:00"}, "8com/ykd5jjCSxAe+IGpZu2AkfEiH48bxy4+E6XF+QU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\pq85vvh46t-jd9uben2k1.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-08-11T10:06:34.1708756+00:00"}, "bHovHeOqkj5xdj+gtyvgh9gmoqV/vKAwdLydiouJaNk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\wm9wbypqec-khv3u5hwcm.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-08-11T10:06:34.1677779+00:00"}, "TvoFj2OilzwE5lmxnlCqonbtIGX/CjSoezdbYH5OewQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\sellbaqaxa-r4e9w2rdcm.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-08-11T10:06:34.1653433+00:00"}, "F1AyL/3P0cM29i14cs1/UyfTVdcRUu7YA/qvYlJASuM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\ix181bhp4y-lcd1t2u6c8.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-08-11T10:06:34.1818007+00:00"}, "KVws80sSwThsRvzxmEl0EPTIURD2qIxr4Pa0boutkoE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\qdpaonnvru-c2oey78nd0.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-08-11T10:06:34.1730681+00:00"}, "bwlsCH3fu9VGp1ifUpojt2pIyZojXNqAYLgMTjcnuPc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\l8tofvflbn-tdbxkamptv.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-08-11T10:06:34.1687852+00:00"}, "JeMUG4hgwBqb9LJR7jwGt3pi/qDbslQCwBM1nU6gyvs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\6e96ofz0xa-j5mq2jizvt.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-08-11T10:06:34.1672713+00:00"}, "zNA9z8j68v/GLPy6voKt9kDOHYOyHXJ67ediao4gTho=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\qtj6vtw0nd-06098lyss8.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-08-11T10:06:34.1584119+00:00"}, "FTUDgUPu0ecT6Nk5ogGQAOyYCnO2vYMpxihD3mhXxRM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\dbcclxakkw-nvvlpmu67g.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-08-11T10:06:34.2014669+00:00"}, "d7JA79nFFkNPBLCj+oUQUWOu5GIIVYGIQ6lUn4+A6yI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\nhrch8h37i-s35ty4nyc5.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-08-11T10:06:34.193469+00:00"}, "WvboPssjeZl8dUEPEXxmk1Rp5UzZjrxa9ksNkWsb65U=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\ilinj6i829-pj5nd1wqec.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-08-11T10:06:34.1828503+00:00"}, "KuyNSMv3KXk4U7QKrzaD7edk3YXxUc1zdG3oBCxWBB8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\7cm1wfiys2-46ein0sx1k.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-08-11T10:06:34.1697854+00:00"}, "9BANXqaK+8y6SL3/kbr8qlrzYpLUG5Qz0DNzJBp5YJY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\rmu5iagomh-v0zj4ognzu.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-08-11T10:06:34.1653433+00:00"}, "FPMjiwnEbXNWhVSdaJ9jcI2oBCO5XMYf7ltHuvsOIYg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\ovuk4oicfx-37tfw0ft22.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-08-11T10:06:34.2065898+00:00"}, "YLk/BzGg9/L1lKle70OaJns5dRKnfiboHDeXXnRhKcM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\tjgqr9epjp-hrwsygsryq.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-08-11T10:06:34.2014669+00:00"}, "KoXk0u2VSXUzNoNPPhce9EV98ryBPNqqP//hlzDTvxs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\cfmww4yjcd-pk9g2wxc8p.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-08-11T10:06:34.1863216+00:00"}, "A28cLQ+9e3P2Ja7nF2MV6HkfEEHxXe68EKwYS2hHnQw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\c8ehi8me58-ft3s53vfgj.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-08-11T10:06:34.1784629+00:00"}, "DRX17Jz5Ff6CIX2nvnOhkSRzV5nT8AU5BPAhAfAgUvA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\61wn5k8mma-6cfz1n2cew.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-08-11T10:06:34.1672713+00:00"}, "0zNU+t25/A0V72cTiia1JZdzohbQ3144vsGhc3UU/4E=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\3w18qcgqbv-6pdc2jztkx.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-08-11T10:06:34.209582+00:00"}, "4R1d0ME1oVVWzTDBkKKJSEsauBS0lcbZx4PTbUeTYZw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\i9vqlloia8-493y06b0oq.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-08-11T10:06:34.1919561+00:00"}, "qH0kf9149T5xj6sE9fdPX7SY0i7zt80Ylgw4wBrM+fg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\xyjekh6151-iovd86k7lj.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-08-11T10:06:34.1924614+00:00"}, "UNtgfFpk5E0UF1oGQi1rPCUOgl/s9OltPZwo85fQ8Kk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\gs6qiabfux-vr1egmr9el.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-08-11T10:06:34.1802808+00:00"}, "UN93HyAbWVnVknvpga1yURHRceKIEtWB1kd0MjkeR/U=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\al4fw0ow1k-kbrnm935zg.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-08-11T10:06:34.1697854+00:00"}, "V/VrohiTHhVMIDYLcFekWKO19RvTFtt/LifW8Jw3Z6c=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\1he1gxmvb3-jj8uyg4cgr.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-08-11T10:06:34.1853023+00:00"}, "lkdYtu5AlgOzIDcL+D3HXCZeLEJZ6ednXF8wGe5NXFs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\w6yic8hrgu-y7v9cxd14o.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-08-11T10:06:34.1802808+00:00"}, "8uKQ8JcC8+IhMyGinIm6+vBcTTV5rae3MdNxgFPiH2Y=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\atf70902cn-notf2xhcfb.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-08-11T10:06:34.1697854+00:00"}, "zEeIXZhyzEjQrmMSqubEAADm6T8jLuivbWcMU7b1BuM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\alqhmjq9pq-h1s4sie4z3.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-08-11T10:06:34.1663633+00:00"}, "4eOrnGnsGtc8Hie1C0UeWiXUlsack3i1IS4k7hS7dDo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\mx05ib7p15-63fj8s7r0e.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-08-11T10:06:34.1571439+00:00"}, "eF7GpIOSlPa8u99sbhCaG4SJNI4M3oOGZuYSKbsYMCI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\tgopaorcaz-0j3bgjxly4.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-08-11T10:06:34.1965724+00:00"}, "MyuRSRZkjPT7elignjGbyKlRj3VgOr0dUoEnddu74Jw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\6omr1nulpi-47otxtyo56.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-08-11T10:06:34.1883366+00:00"}, "G62S9aPb6qTRRu7VdM6HlBqQlyUYnj36rpg7O3borzo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\q34pds2tf9-4v8eqarkd7.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-08-11T10:06:34.1924614+00:00"}, "whSD9LBLyIgWf9OikSVIGEPRrS4WpwwklEgVGqvkKqQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\x5sya4yjma-356vix0kms.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-08-11T10:06:34.1853023+00:00"}, "uqACf/ZvJrsRl8ONPyZjPGXEbEP2LhJYwncZA4o/hsc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\e6ha2kb8yt-83jwlth58m.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-08-11T10:06:34.1584119+00:00"}, "Qt9nayHvrrUYFqZCuRH/TAYQEfIt4EpBMd+5SxSSCGM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\n2kdrs17qm-mrlpezrjn3.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-08-11T10:06:34.1995804+00:00"}, "C4glN6bTw1nSlBAlDOg0G13CYoinN4F0/fl8m99fF24=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\12hqi648ev-lzl9nlhx6b.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-08-11T10:06:34.1919561+00:00"}, "Lw77c+gIeBYdFT3Ju/IecJETR4j946M/yMsZ4L8AAxU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\7qorf53x51-ag7o75518u.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-08-11T10:06:34.1842624+00:00"}, "E8MFLOxhmUTfDcQUQpBzwrdQLQDJZ4yDxdhY55Q52Ww=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\9689a7o82a-o1o13a6vjx.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-08-11T10:06:34.1715771+00:00"}, "eWCJlPYlFH/N2n4xQmxyoL4UBaTN3toMmuRsQMncrZc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\jmdhlcauwa-ttgo8qnofa.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-08-11T10:06:34.1677779+00:00"}, "oF7dJrL6NE53NbzaVgI34Du4tcIsGMuTGUTet0VchFQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wf4wcyqcqt", "Integrity": "fNKkLSNFLZMVTc2zVrpMXQqOwPU3sU4GWcfEbCR2V1I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\css\\site.css", "FileLength": 319, "LastWriteTime": "2025-08-11T12:21:40.4795988+00:00"}, "LWIA+2XfUnkGfiEoKe9KTRlJorj0iUmLE3gnuS+vyfo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xngppdbdhq", "Integrity": "nR0lNhVJOqtsVYCBNYXOoTLPTwP88AJKUlf4evu9xkE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\favicon.ico", "FileLength": 9431, "LastWriteTime": "2025-08-11T12:21:40.4871989+00:00"}, "xQjQL3ApQet36FYWzrn62wv2UPGqIjewHjaha23vBTQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fpxp8wntf7", "Integrity": "465WqepNnI4ENLgQJFXrL2gtI20GCfeLK3JAtugOzpA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\js\\site.js", "FileLength": 190, "LastWriteTime": "2025-08-11T12:21:40.4891986+00:00"}, "xi130j2LMFGmjQBIB3Lg3sMflUMOgIdr9g9Jfe+Ybac=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p1uwaguxw6", "Integrity": "HQp0c6N8q5wOxo27/7+5nSrp8SBfCepJdWIMB3pb5bE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6915, "LastWriteTime": "2025-08-11T12:21:40.4871989+00:00"}, "tDRl3BddYCm00Vz84/GKOzC5rjbxWPFBDmPLYCDGG5k=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bpa5g0wlhg", "Integrity": "keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 33080, "LastWriteTime": "2025-08-11T12:21:40.5030495+00:00"}, "og8ZRwefqMD8tlmc/yScFYhGG5eQmHXkPt701+w/eOI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s32t06avo3", "Integrity": "6OfodiC592J173EssQMUTEfylPvm7++5l9PRY+7T5Uo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 6102, "LastWriteTime": "2025-08-11T12:21:40.5076384+00:00"}, "8UrRPhswp3x6AzHRYIpYGoJHZNt1mHDxRhXV7qCwqWQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3latwtrz94", "Integrity": "1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 14072, "LastWriteTime": "2025-08-11T12:21:40.5131582+00:00"}, "LK1GrrG4qpo4yYj2ID/qcJJmtivYh4t1WlN4HqI4jqA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "316ql842l5", "Integrity": "+UkMsfq0zPH03cQtfw9papLT2sHleN3bw7S1s5iLonY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6918, "LastWriteTime": "2025-08-11T12:21:40.5169512+00:00"}, "rYxNTcYUTtrIynSAgxRl4bzVMnAD52aPxNCFAkhQZVM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnst782kog", "Integrity": "ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 33061, "LastWriteTime": "2025-08-11T12:21:40.5304643+00:00"}, "HqSZK9v/enElUbscvI+Pd0GCvBCROJFSUDmfjltxvDY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ykjynei6kc", "Integrity": "Xll87KRp2km7kw+kDSFiBiyUQnZ7QCVIb8yJtRDXckQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 6104, "LastWriteTime": "2025-08-11T12:21:40.532871+00:00"}, "gI15KBGYmCmkgrzlcBOFjDYLRieeTOc9RgvUM9rHFUo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f178oapzb7", "Integrity": "XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 14093, "LastWriteTime": "2025-08-11T12:21:40.4932014+00:00"}, "KqLbBmK6Ek9dnZ33MkwLugteEH93Uo/iIZP1IXv4ZoQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "syqesifl59", "Integrity": "/Cti7KtgK7n0FoWSWRMvmhTwGSxieEUp+Ao+6Xxnmug=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3375, "LastWriteTime": "2025-08-11T12:21:40.4851995+00:00"}, "9IoazI46mhnJREFR1kxNT8pck1fi5jYvKdLZtClXlUs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5b7ig2cj79", "Integrity": "fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25714, "LastWriteTime": "2025-08-11T12:21:40.5004174+00:00"}, "2WhxEh3MZvmQTwqF7eoB/RfcbctXsIYqA9JR0FuKnsg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5fx04t62wt", "Integrity": "z14pI3bmpzGTIVaJu6aKOcry1zSAu/H44LHt9J3bXFM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3199, "LastWriteTime": "2025-08-11T12:21:40.50247+00:00"}, "CQSJhy3cKe3XUd/i4M+wFJKE15kgd1PIo75TGD4fbJU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ggfb0v5ylw", "Integrity": "ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12568, "LastWriteTime": "2025-08-11T12:21:40.5065703+00:00"}, "I68iIco35/vzdqdsjZ5tX34YfV5mBhC7zqQCaals8nc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vkbr9nomgm", "Integrity": "oSfmOJQjIULAN8mPr84YlRct/JVoFbWAn9Y7IWzBuoc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3364, "LastWriteTime": "2025-08-11T12:21:40.5086385+00:00"}, "px4oObUCEyb7RKQjfMwUUAB5bSBjTVy4xVSQZJ05e7k=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kqk14ew2nl", "Integrity": "PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25726, "LastWriteTime": "2025-08-11T12:21:40.5169512+00:00"}, "k7C+fQspC4daBbpnaeoVX719ODwMFSRgqXrRWxatLHM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b8ltiz8u9h", "Integrity": "u85nx859JKxhSe8TTSe97CuakJmuU36EC4JdxkCZL3g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3231, "LastWriteTime": "2025-08-11T12:21:40.5195007+00:00"}, "6dsJV436InviulBJm4djEHu0ajqoAaqqsvQhytWikiY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ldvkm706vq", "Integrity": "J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15028, "LastWriteTime": "2025-08-11T12:21:40.5236437+00:00"}, "f+KNysSrsvdOF73ibQWgwNyRlOMQEkcc+TLZ+KFzPdo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g3lpifp44y", "Integrity": "9EqjQR8ugEerwGk0t0elN3RQ+XHT25kPo1+/yYXwvNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 12127, "LastWriteTime": "2025-08-11T12:21:40.4891986+00:00"}, "YC1vTZGZfVqR1kM+PSabgTE3GXLPTwk8nxJBcptmanE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21g01g2f66", "Integrity": "JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44266, "LastWriteTime": "2025-08-11T12:21:40.50247+00:00"}, "cxP48f7a5buiQ970uCV+LR9X3ZlsU5nKBL5o81gTp/8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cy4j794sg8", "Integrity": "LPVzscuhkQC3vZzaHKyyJLBxtZ0JDszgPmlT3zj1wGg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11149, "LastWriteTime": "2025-08-11T12:21:40.4911982+00:00"}, "FbJo8wdSE9KvbkfAHKTRvu4yB2NknaH4As03jfNMHUY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oiolxl3gck", "Integrity": "oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24487, "LastWriteTime": "2025-08-11T12:21:40.5045731+00:00"}, "nRPNxhBe20a77aa5RfHWGbw5AbTNEwl7jQY1eTZishQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "letd63iq5t", "Integrity": "49exWGtFTeHrxPgcV2SALyeXqbJ0Gx2BAW7ghwTbDHs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 12069, "LastWriteTime": "2025-08-11T12:21:40.5086385+00:00"}, "EgAk2xeuuQHkW3lxJH/y/FNI6LUYk37920M2Gg6HQMY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pnmw3mh9ht", "Integrity": "tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44237, "LastWriteTime": "2025-08-11T12:21:40.5226456+00:00"}, "A5UoW99qPGtPx7gsLJon6F0vaCKsruzqvssotGTzNbU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ukjvmro89", "Integrity": "QDcr93RhSeRIcvSG2UO4XpXn7SmN9PibCcICeplm1tk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11127, "LastWriteTime": "2025-08-11T12:21:40.526663+00:00"}, "ru2lAgZLiYifdJpPFh1YDQbRdgDaq1rUF0Bu+6NUT68=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0gru265j2n", "Integrity": "cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24456, "LastWriteTime": "2025-08-11T12:21:40.5340215+00:00"}, "8nWo7LHnHIz3EboMmClK4+Vry+8k+5p+W1O9s7YRtO0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dp6dicxiq4", "Integrity": "MfXRRxefGD0S0k5f3gXwZNQW7ELiStY01dZj+IbLlaw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33478, "LastWriteTime": "2025-08-11T12:21:40.545632+00:00"}, "GGgadbD1dtdAhSbRYZQ9bvjZuy5GOtxiOM8bxlpPrjQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glev5uv9kg", "Integrity": "pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 114902, "LastWriteTime": "2025-08-11T12:21:40.578601+00:00"}, "oDLszwxUMYHggG8ua0oCZ5PCsjN/hGT2yJFjXzHxjSI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uhnhmx07wd", "Integrity": "zrRIXAC8ugCIlsRMgBBjTa8xli0BiiAqT375rZab79Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 31118, "LastWriteTime": "2025-08-11T12:21:40.5867421+00:00"}, "GbIXEacn/z1OMAxzE+cM3YZQiNYoMxsExQkW+bwS4w0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77t5enldb7", "Integrity": "Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91960, "LastWriteTime": "2025-08-11T12:21:40.5154439+00:00"}, "FKh47YFLl3HFP0QiPLGPBwbwwFa8rhpU14tbhcHrtWc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7kvveri0mt", "Integrity": "h+uVSG7EhchzF96LorZkFbGFBO8oQowufr5PhxsGq8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33366, "LastWriteTime": "2025-08-11T12:21:40.4967217+00:00"}, "6Q0bFM/ZSExkUav7DkiwhgCO6ARAp4vRm/TLJpf2uig=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2knqut3gi", "Integrity": "YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114877, "LastWriteTime": "2025-08-11T12:21:40.5298709+00:00"}, "YHUwzaXcwGL3I7mIlYSgg8IXgFw7TqwzQNLNgsn+OxQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3m4n8jcl3h", "Integrity": "5VQx+pcREAWRC7fSQ1OVsJtpTGF7SJ83RS8p2rovHHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 31137, "LastWriteTime": "2025-08-11T12:21:40.5390198+00:00"}, "3wJCqFYIsqvvDBbe4YYGwbWm9G+9AdrKSSMmixxyEFY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uom9ela1zq", "Integrity": "4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91866, "LastWriteTime": "2025-08-11T12:21:40.5681772+00:00"}, "+KbgxMc/8XhMVcy58LSeoYMBFdjRPjU+G3cMOkQG+I0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zqu1wssclk", "Integrity": "Fd462fjZQtrrvL/sHbIIZF7ktZgtHombUabahz+Nhoo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44461, "LastWriteTime": "2025-08-11T12:21:40.5817701+00:00"}, "9DnjL7jbg5RC8WxGRzsCxPjwlsvGdQ5mBbJu6n5mHyg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iwjfk8z8sl", "Integrity": "fgl+pethjpTJQrRxrFinuKNP9hP56Cpi71nXoWFk1oE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92277, "LastWriteTime": "2025-08-11T12:21:40.6143765+00:00"}, "JU/ldRnk93X+mmDMEagIkvWaEdImCcR7YQ4lPmjOdZU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "18qy0lzppu", "Integrity": "QZ0MSDfNrGl5pbF5ppDikD9YeYfOenEETYBSBemyReU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23799, "LastWriteTime": "2025-08-11T12:21:40.6200589+00:00"}, "BnJRuJH0VZb6dF1GIThTUkRTwJhKrqD+Wc1OC3MLueM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4jjusqppj5", "Integrity": "YNK7tH4KLEhmt17m2X41sl74ZCDUilGpyhRBJKwG0P4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86518, "LastWriteTime": "2025-08-11T12:21:40.6473921+00:00"}, "hco1cMVlkPe7qNhvwFf5DbJAa+rqqs7gE6MHM3R8vbQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9z27fl73l7", "Integrity": "K+JV1bWtCSH6pUbk4PUJUao1Gc0EagazN8O9VnhFaPE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28886, "LastWriteTime": "2025-08-11T12:21:40.6569181+00:00"}, "pCQCPBqlIK9+wozoc0VbnIZtiwWkISdtsC0iznDFGo0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6e2hoibiq5", "Integrity": "oglZ6aWY/UXq+QsVhwhOwfIZbgvz7Dvjg/GfXmQsBVM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64234, "LastWriteTime": "2025-08-11T12:21:40.5030495+00:00"}, "09Elg1WHwxk+XhCzZi84IsM0H/NjK/tYiqwJKgHKhm8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6t36crcw3z", "Integrity": "Ng/NTAPb2HvIa++U+TsQnZ6UYlXnzyFuh5Df7vHJZgU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18542, "LastWriteTime": "2025-08-11T12:21:40.4901973+00:00"}, "2BG7ivGIFQvvWerzBDFw6k4W7BLdBBMnm3XG8Y4Kwbw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "03mhy7u5rs", "Integrity": "9Hc7N6ug4kO+ZAzS1pFxw7Hbp+FZaCKMnDLRAv1J/zw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56385, "LastWriteTime": "2025-08-11T12:21:40.5096377+00:00"}, "eaHnal5sDJv8Z4FIeWLR1wH7OPcBz0Ctp4C8Oh0UuVQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "winy7sfnv8", "Integrity": "mLLuMjFYOIYX8xSc8UD/TLCKJOjlhcscgrxC5va1aAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29657, "LastWriteTime": "2025-08-11T12:21:40.5195007+00:00"}, "3eQvJ0acGgXMXKGt+ChL7KRQ2i7z10RSIkILspnzV4I=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fizolni560", "Integrity": "OeWySIbjCJXM4ZgVwiGmsjpLeFsT+qOpONsDqh8xk5M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64490, "LastWriteTime": "2025-08-11T12:21:40.5360188+00:00"}, "NqZYbSPl90h2MMtI6t9z5cmMtPTNMwTkK9ZA6rrAXkY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnp63td8x7", "Integrity": "YQ6ZOC5+9dlRHEgMrLJKheWx2C2Xk7AL5Hy36EOfqq8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16571, "LastWriteTime": "2025-08-11T12:21:40.541023+00:00"}, "kHi/mAHG9I/eUf6pgi0rV35q09PkcTV3diD0qYDd5i4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s9vzkbm4vd", "Integrity": "EImWkSMXEOh40HFlhmq90VTXlOCYqWVzWn4vPxUhiA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55524, "LastWriteTime": "2025-08-11T12:21:40.5587137+00:00"}, "SvDBWvq5axwhDHmbDoWN7yVmno9qzPDNxFb6DhnsyKU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hj59c5yssu", "Integrity": "TWRCXMwzQLwwVw7TcG+7g24PsqDnarIGBgITeMiCp10=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4663, "LastWriteTime": "2025-08-11T12:21:40.5619446+00:00"}, "nxocVea7w/IwetyT1M61jtqpLrpgjij5U31IzeHKhA0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tabrq1ho0f", "Integrity": "9X+6mMpWl0sRImYav09zbU/QCCt+GeH7cDib6C/z2lI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2202, "LastWriteTime": "2025-08-11T12:21:40.5630421+00:00"}, "wjIjD4WLz5qgdesj5jH6IxdnbyfgyFjzP5gl5mc6Gm4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0vtotz30el", "Integrity": "1HkjQSVuV0/ji/X/lGeDTHeagBS0BwopMrHDQuwqgtU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 680, "LastWriteTime": "2025-08-11T12:21:40.5661766+00:00"}, "PiUzPONeJ4JLOwWlnbha+ft/M6RSA09TmfYXrVvMhIA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pvtge0zj2y", "Integrity": "u0Z1XIrPIXG0QZK+EQOG85U9O7JhC+GVwFW+Rcyvbyw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 13955, "LastWriteTime": "2025-08-11T12:21:40.4851995+00:00"}, "ewQnCKxtcsW9Z2gOufm70oCZ326J6R4gMA8coNG/Lcg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ka6j29rtmm", "Integrity": "nJwi+an4Wr4A7wVYax2DH+rKvwNbT+YQBBWTeP8GLr4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6382, "LastWriteTime": "2025-08-11T12:21:40.4891986+00:00"}, "MGamQQ9sD1ACeDZ6wnyc4G0AXJETbWg285wNNwZy8AM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w44s95dr06", "Integrity": "8HCMA1neUf8Vrpg7qfQgzCVX6n/hFKgcFl5RKQuVC1k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14023, "LastWriteTime": "2025-08-11T12:21:40.4967217+00:00"}, "DMt6kkRFKIcKbhSVkmLkr2MGUr7B/DqwYRqItVDQ3qA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m7rg4chp1z", "Integrity": "Po4sWHCLIbaqYqyEYnIYtBO+dRjwxtbreif7YdpGQxM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8053, "LastWriteTime": "2025-08-11T12:21:40.4952015+00:00"}, "ePLQiq/MXvUfqBPMEaAME0Omt9hQrSQJspG1X7gtsdE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tbedkqp182", "Integrity": "yZU1lf/p2dEnCHUXYp8G4roAyod23x5MoR4DehwHfrA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 670, "LastWriteTime": "2025-08-11T12:21:40.4967217+00:00"}, "u8ySzkjSMLvCkI5sSfXSV15df2tBBzVYV5lNxrXHMjc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v75tr20pas", "Integrity": "q0KM+9yOLRYeTH/iGBE/HUYO08aXn2DmZ+yyIU6/kho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84055, "LastWriteTime": "2025-08-11T12:21:40.5195007+00:00"}, "8DOwlQhebPDGJa17CD8EPeCs1hzoPrLHSYMA2pbgGfA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9lbv9l0z68", "Integrity": "bSlV8nIWgnEsf0ljiCRK0xw364UXDcqZZilq1CQ+At0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30413, "LastWriteTime": "2025-08-11T12:21:40.5286634+00:00"}, "E1NYCZFouwYm9Ztmgzw6GlPcUNJ6p4UXEWMMGn0p4z0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sdwt97mj8r", "Integrity": "0HH7P7Mtrs+2sBMSFXD8DJw0oV4iDB5wkeRFpSi113A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 53929, "LastWriteTime": "2025-08-11T12:21:40.5076384+00:00"}, "FW1Ttts1+vhdsF15LuWSQiHNBbWuv14GCGXlTqbtyYI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yow8h3bl0n", "Integrity": "fhspAbITSYOUuRd6s5oYdr7V1DC3ibPbW5Siv7MGps8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68315, "LastWriteTime": "2025-08-11T12:21:40.5256433+00:00"}, "phXvrzUehfUjM8wObaiqy3G6AXPMnS89H6H8yx+R6jQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gm2ug2dj20", "Integrity": "OK9mKWiOoFmOZ+xYop9MRSKE6LHMu/9ZTCPvluUOpcA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24089, "LastWriteTime": "2025-08-11T12:21:40.5304643+00:00"}, "blal3NtH+XpHUPD16C+IlcLStRKK5cFMDEiKUQECrhU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uiwq7himce", "Integrity": "2u2XFgZyC96dP/elrO5ny/maD/llVl8JsX3pBm2SWT0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 42735, "LastWriteTime": "2025-08-11T12:21:40.4977232+00:00"}, "xGPQbO45CGSvRO8qAJV6mgHJA/Mx/hLYR5b5vJMCG6Y=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rdl0higpfn", "Integrity": "wRqs30CSrwWDvG1bDMUjomAlSqRRTMplLSuLcrgYtHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.0\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 669, "LastWriteTime": "2025-08-11T12:21:40.4952015+00:00"}}, "CachedCopyCandidates": {}}