﻿<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - سجلك الصحي</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/HealthTrack.styles.css" asp-append-version="true" />
    <!-- Google Fonts for Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container">
                <a class="navbar-brand d-flex align-items-center" asp-area="" asp-controller="Home" asp-action="Index">
                    <div class="logo-placeholder me-2">
                        <i class="fas fa-heartbeat text-primary fs-2"></i>
                    </div>
                    <span class="brand-text">سجلك الصحي</span>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                        aria-controls="navbarNav" aria-expanded="false" aria-label="تبديل التنقل">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link active" asp-area="" asp-controller="Home" asp-action="Index">الصفحة الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#records">سجلاتي</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#doctors">الأطباء</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#about">من نحن</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#contact">تواصل معنا</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        @if (User.Identity!.IsAuthenticated)
                        {
                            <div class="dropdown me-2">
                                <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    مرحباً، @User.Identity.Name
                                </button>
                                <ul class="dropdown-menu">
                                    @if (User.IsInRole("Admin"))
                                    {
                                        <li><a class="dropdown-item" asp-controller="Admin" asp-action="Index">لوحة التحكم</a></li>
                                    }
                                    else if (User.IsInRole("Employee"))
                                    {
                                        <li><a class="dropdown-item" asp-controller="Employee" asp-action="Index">لوحة التحكم</a></li>
                                    }
                                    else if (User.IsInRole("Patient"))
                                    {
                                        <li><a class="dropdown-item" asp-controller="Patient" asp-action="Index">لوحة التحكم</a></li>
                                        <li><a class="dropdown-item" asp-controller="Patient" asp-action="Profile">الملف الشخصي</a></li>
                                    }
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item">تسجيل الخروج</button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        }
                        else
                        {
                            <a asp-controller="Account" asp-action="Login" class="btn btn-primary me-2">تسجيل الدخول</a>
                            <a asp-controller="PatientRegistration" asp-action="Register" class="btn btn-outline-primary">تسجيل مريض جديد</a>
                        }
                    </div>
                </div>
            </div>
        </nav>
    </header>
    <main role="main">
        @RenderBody()
    </main>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
