is_global = true
build_property.TargetFramework = net9.0
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = HealthTrack
build_property.RootNamespace = HealthTrack
build_property.ProjectDir = C:\Users\<USER>\source\repos\HealthTrack\HealthTrack\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\source\repos\HealthTrack\HealthTrack
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Account/Login.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxMb2dpbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Admin/CreateEmployee.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWRtaW5cQ3JlYXRlRW1wbG95ZWUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Admin/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWRtaW5cSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Admin/ManageUsers.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWRtaW5cTWFuYWdlVXNlcnMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Admin/PendingApprovals.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWRtaW5cUGVuZGluZ0FwcHJvdmFscy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Employee/CreateMedicalRecord.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRW1wbG95ZWVcQ3JlYXRlTWVkaWNhbFJlY29yZC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Employee/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRW1wbG95ZWVcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Employee/PatientsList.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRW1wbG95ZWVcUGF0aWVudHNMaXN0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Employee/RegisterPatient.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRW1wbG95ZWVcUmVnaXN0ZXJQYXRpZW50LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Home/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxQcml2YWN5LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/PatientRegistration/Register.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUGF0aWVudFJlZ2lzdHJhdGlvblxSZWdpc3Rlci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/PatientRegistration/RegistrationSuccess.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUGF0aWVudFJlZ2lzdHJhdGlvblxSZWdpc3RyYXRpb25TdWNjZXNzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Patient/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUGF0aWVudFxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Patient/MedicalRecords.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUGF0aWVudFxNZWRpY2FsUmVjb3Jkcy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Patient/Profile.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUGF0aWVudFxQcm9maWxlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Patient/ViewMedicalRecord.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUGF0aWVudFxWaWV3TWVkaWNhbFJlY29yZC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Shared/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXEVycm9yLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/HealthTrack/HealthTrack/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = b-rvqxkrtq0i
