@{
    ViewData["Title"] = "الملف الشخصي";
    var user = ViewBag.User as HealthTrack.Models.Entities.ApplicationUser;
    var insuranceInfo = ViewBag.InsuranceInfo as HealthTrack.Models.Entities.InsuranceInfo;
}

<div class="container py-4" dir="rtl">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user me-2"></i>البيانات الشخصية
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">الاسم الكامل</label>
                            <div class="form-control-plaintext border-bottom">@user?.FullName</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">رقم الهوية</label>
                            <div class="form-control-plaintext border-bottom">@user?.NationalId</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">البريد الإلكتروني</label>
                            <div class="form-control-plaintext border-bottom">@user?.Email</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">رقم الهاتف</label>
                            <div class="form-control-plaintext border-bottom">@user?.PhoneNumber</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">الجنس</label>
                            <div class="form-control-plaintext border-bottom">
                                @if (user?.Gender == HealthTrack.Models.Entities.Gender.Male)
                                {
                                    <span class="badge bg-info">ذكر</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">أنثى</span>
                                }
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">تاريخ الميلاد</label>
                            <div class="form-control-plaintext border-bottom">
                                @user?.DateOfBirth.ToString("dd/MM/yyyy")
                                @{
                                    var age = DateTime.Now.Year - user!.DateOfBirth.Year;
                                    if (DateTime.Now.DayOfYear < user.DateOfBirth.DayOfYear)
                                        age--;
                                }
                                <small class="text-muted">(@age سنة)</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">الحالة الاجتماعية</label>
                            <div class="form-control-plaintext border-bottom">
                                @if (user?.MaritalStatus.HasValue == true)
                                {
                                    switch (user.MaritalStatus.Value)
                                    {
                                        case HealthTrack.Models.Entities.MaritalStatus.Single:
                                            <span>أعزب</span>
                                            break;
                                        case HealthTrack.Models.Entities.MaritalStatus.Married:
                                            <span>متزوج</span>
                                            break;
                                        case HealthTrack.Models.Entities.MaritalStatus.Divorced:
                                            <span>مطلق</span>
                                            break;
                                        case HealthTrack.Models.Entities.MaritalStatus.Widowed:
                                            <span>أرمل</span>
                                            break;
                                    }
                                }
                                else
                                {
                                    <span class="text-muted">غير محدد</span>
                                }
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">تاريخ التسجيل</label>
                            <div class="form-control-plaintext border-bottom">@user?.CreatedAt.ToString("dd/MM/yyyy")</div>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(user?.Address))
                    {
                        <div class="mb-3">
                            <label class="form-label text-muted">العنوان</label>
                            <div class="form-control-plaintext border-bottom">@user.Address</div>
                        </div>
                    }

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">حالة الحساب</label>
                            <div class="form-control-plaintext border-bottom">
                                @if (user?.IsActive == true && user?.IsApproved == true)
                                {
                                    <span class="badge bg-success">نشط ومفعل</span>
                                }
                                else if (user?.IsActive == true && user?.IsApproved == false)
                                {
                                    <span class="badge bg-warning">نشط - في انتظار الموافقة</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">معطل</span>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>معلومات التأمين
                    </h5>
                </div>
                <div class="card-body">
                    @if (insuranceInfo != null)
                    {
                        <div class="mb-3">
                            <label class="form-label text-muted">شركة التأمين</label>
                            <div class="form-control-plaintext border-bottom">@insuranceInfo.InsuranceCompany</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">رقم البوليصة</label>
                            <div class="form-control-plaintext border-bottom">@insuranceInfo.PolicyNumber</div>
                        </div>

                        @if (!string.IsNullOrEmpty(insuranceInfo.GroupNumber))
                        {
                            <div class="mb-3">
                                <label class="form-label text-muted">رقم المجموعة</label>
                                <div class="form-control-plaintext border-bottom">@insuranceInfo.GroupNumber</div>
                            </div>
                        }

                        @if (insuranceInfo.ExpiryDate.HasValue)
                        {
                            <div class="mb-3">
                                <label class="form-label text-muted">تاريخ الانتهاء</label>
                                <div class="form-control-plaintext border-bottom">
                                    @insuranceInfo.ExpiryDate.Value.ToString("dd/MM/yyyy")
                                    @if (insuranceInfo.ExpiryDate.Value < DateTime.Now)
                                    {
                                        <span class="badge bg-danger ms-2">منتهي الصلاحية</span>
                                    }
                                    else if (insuranceInfo.ExpiryDate.Value < DateTime.Now.AddMonths(1))
                                    {
                                        <span class="badge bg-warning ms-2">ينتهي قريباً</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-success ms-2">ساري</span>
                                    }
                                </div>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(insuranceInfo.CoverageType))
                        {
                            <div class="mb-3">
                                <label class="form-label text-muted">نوع التغطية</label>
                                <div class="form-control-plaintext border-bottom">@insuranceInfo.CoverageType</div>
                            </div>
                        }

                        @if (insuranceInfo.CoveragePercentage.HasValue)
                        {
                            <div class="mb-3">
                                <label class="form-label text-muted">نسبة التغطية</label>
                                <div class="form-control-plaintext border-bottom">@insuranceInfo.CoveragePercentage.Value%</div>
                            </div>
                        }

                        <div class="text-center mt-3">
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>التأمين مفعل
                            </span>
                        </div>
                    }
                    else
                    {
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-shield-alt fa-3x mb-3 opacity-50"></i>
                            <p>لا توجد معلومات تأمين صحي</p>
                            <small>يمكنك التواصل مع الإدارة لإضافة معلومات التأمين</small>
                        </div>
                    }
                </div>
            </div>

            <!-- معلومات الاتصال -->
            <div class="card shadow mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-phone me-2"></i>تحتاج مساعدة؟
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-2">
                        <i class="fas fa-phone me-2 text-primary"></i>
                        <strong>هاتف:</strong> ************
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-envelope me-2 text-primary"></i>
                        <strong>البريد:</strong> <EMAIL>
                    </p>
                    <p class="mb-0">
                        <i class="fas fa-clock me-2 text-primary"></i>
                        <strong>ساعات العمل:</strong> 8:00 ص - 8:00 م
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="row mt-4">
        <div class="col-12 text-center">
            <a asp-action="Index" class="btn btn-primary me-2">
                <i class="fas fa-arrow-left me-2"></i>العودة للوحة التحكم
            </a>
            <a asp-action="MedicalRecords" class="btn btn-success me-2">
                <i class="fas fa-file-medical me-2"></i>سجلاتي الطبية
            </a>
            <a asp-action="MedicalVisits" class="btn btn-info">
                <i class="fas fa-calendar-alt me-2"></i>زياراتي الطبية
            </a>
        </div>
    </div>
</div>

<style>
.form-control-plaintext {
    padding: 0.5rem 0;
    font-weight: 500;
}

.border-bottom {
    border-bottom: 1px solid #e3e6f0 !important;
}
</style>
