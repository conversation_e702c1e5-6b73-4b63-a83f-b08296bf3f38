{"GlobalPropertiesHash": "OidH1Bsft7ZFomO+89jKFVawliuLdtnfCn5LvIWdAug=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["m6sCbpJUAIZCXi867xKFgHmM/1ErpIjeT7MSoA48Jrw=", "geiBVc3B0D5jvH0Dx3MXYeagSjM2ohBpxKGVtwUIef4=", "stHbJU+YK+0ceinQoS3616JDt1ZLFY50uIKL3VKgvsQ=", "PH3RS0Yri96ZCuYBykcU/Tpz3NBcw1wU+ldDjnXcS6s=", "GEpWryBlNijdzZuD52/3Kfh37gaFoEAMjlrC4zeWj4I=", "+sQefBBt2JsRaIqOaCUb9DWdJc9l7m7h3vtjcMzaUMM=", "Z8mKPIFxezB5WUUeKEGnkwNRwem9PI9jqLzXBUK3Hzc=", "BmhVFwltUHY6Z918sNQnRixFsuxEOjibNJaCXdrFJ3U=", "xFPSGednv3aCGpGXcS0oTLgzVUXihJGp8k753fZuwv4=", "KhohUyU5DQxH4L236vtXwUa5k4eKki8Rpv4VO/ooibM=", "fKEqTgJ/OqwXCqNiUdReOu648qIlGdgbN0MQwtqrxV8=", "ZKRqJ6/X1DhK4eqhnlopqJZb7ycA0gXbK2FxHunnUAE=", "PbX6VqUjbTWcsb3SlI2dvJrToqNEfc0PAgXCCI4Gbq0=", "LnqerUhBTJSa5m9zfFqKwJwuQfchUTwdbYRTsZCHTu8=", "obFiMft0/kwcrPl7a56YS+bS03r1oqmKMa4KMztc7R8=", "i2/4v1MlBprFT/vXzEU3my33IR8WG3Hrd8unfIQFz8s=", "ANYd2jvdSRmhpmXmkpiRqT0tpTjcraVXzv76az/2ILU=", "hkaOo1wevPVhgP41KbgJwoqNtkB1yMRUUEEeExOkPc0=", "3095yeRq8h9noCJMVlJKg8P0ZNDn5eDfPwkNTLlY9LE=", "dEOcb6ugSYsfXVrnIfADvf0WSfGmvBCt1UF0slaViY8=", "nsWN/DMsH+tQVKqZk4OpMJw4ZsjrBY+fpiPZRi9Y0E4=", "hC+H2HN87C//JJSJdTLp4qqQlyat/hREPnp8O20duCY=", "h85exzdjlDRVLlktUHSzJJQ46zRnOtjRLaYuo1Jftgw=", "d8p1bJdEWoKZq01d6vgJQ/YREGc1nvLZiexTOB9pKaE=", "37DPUkwFSlnJxFU01W8IgQbEqJx4eh9tLWaKUN8ptrE=", "YKYR/SoCaa2+MTPVM6/eFyE96y3auEBBe211v77o0gs=", "ccy9AumhvEKwvEhX7IsFHVimiujKEfYKGzEP+Zpjak0=", "Hdlti+uE020rh+yjKNcKvOapFf+kEmcaJAoqbnrQIJc=", "aX3QxH0/iSn90ShANqA0B7SGbMolBpjYXlUX38r31o0=", "2XlahkoO3zQ88tzBMLn0uh9E/KoWjrdTFLBXz9c9+xs=", "j/TcEgzg4I2EoEnrCnfuh0YScagDKY3mt2O73ntf0zc=", "7H048aZTwmjNho69Id10TXaCvC0SBG4I77naBbp7uL0=", "VZlKpEP0NpUjFcuu3p0R9fR0lQhcoU4MCBUrhzySjE8=", "ZPfVUPC1RbJCGCSFjUL3RVPokaygb58xZmyRXOlKJgs=", "bdQ+0Fg/hgWmkBboBIccLVA9XBpa86R6tzunXWE90Fw=", "WJT4GED5kVN35C/Rxg6OmCsnsq5b/THWDbFaDLDXBLA=", "S3LCBrMK5IPKQu62lXYa9cL+ZyQ+DRgwNWyvroELklI=", "b3t5wBdfN9yV2Hwr0BBUF3bhZ2pjnTB9cRvP1/lIAl0=", "IAAjvzEk6+xIgEnMPsF6zM8A8nPlaXLIRPh3zSNvsFk=", "CD+z1Qn+8GdLrOhPbQe7MwHutZk94emE5KPkgUHM/nM=", "Ygep/D/BaO/XfZT0aDLf8TLlvAn+9F4IIRgWgyLphos=", "N4eoHemZle5WUcJPdfT0xGGM0ziZ2qi2G67KLU246Ws=", "EdkY84B/xulfVBFOvn5CtAik0MMRbqfbuMj3466KaPI=", "7bnFk9etT/mbl3OcKRD+xtaS0yf9pCo59X8PTHkOPFs=", "nH9sW89wTUGCbWQceo54vRWvL9/tgy90nAuAdh+XJmY=", "xWY5UB5eHGYeMW1uxtwGd9Z3H1raTo3Zl63qkupBH34=", "nTm03V7IfvpZKa+p774Z6C4pSTcsv0o7jNF+QkU/goc=", "+R3k+S6jRMxIUv0pQ64BGlgbY7uE/XMWQofEU9zGXfw=", "CXX7kjz+0muCWDCyrTGoxNOcoMB21zmyKvQHVCE9fuk=", "QMAFg8TRUeYQY3sQGvINvDKpfWW1zQ/S7gTCFbJ0ctk=", "euUTd3DlZPPcd9yLjZMvm4cN0vz+QEGNd4bsj7cv90s=", "3PsJp1sBb/hZukwmFO52N4RZmPMfyEVhkT175xCi3og=", "AK1grEryw9GeRmCD3cMttd6M0r7DbD2uffqIxM81LrM=", "5R2DLPQINsn0FHl7T7FBLvVfI/Y4g0v+P8ZTqMiq0sc=", "vPi/58Ht67kwofkvslGJfTBBq77B+DcjwBPmaGgFIik=", "YKjqP1xbsfkSLYTYRV/uRAyYQarTyw0WN+xhtl+YRbQ=", "rP32TunN8/nkrC51Wye9LtiNsQF4B8ZnrNMjuoAStLU=", "JqN+300aVLTGXWoS6SaRH7dSdnFwE+8FgYz4eaw6jok=", "3l86Vn9xeR9YHXllf9xArSp3ATAppYO6BrW5ragt+hY=", "1AwQOgVHBBHLICG3EpiskhOZ5YegJkke3yacgjML+H8=", "p++abtvr88olyc8WLdlHMY68swHS+ui118l6waEXb6U=", "rYl13VlauIgsXFksa2oqgV7wzfhwW2cBVYAr9EHuNOY=", "vrVoza8LjMPnst2Na97oO15pHPFR9jy7kuqRr10waII=", "1GpqSYUrg1j95P4OxJpPg4s0QzsQLfFJ7f+CSNig8Wk=", "SU4T1qXkJ8OyU4GN1iyfjkG+Sp1aUcC66VPQIdTmPE8=", "84vEMLweG55S76lZIQnZw1zwZNaFgTB4+gS+IYoy13U=", "5UyI+EOcdkNoMWGnV0d97SUrUn58E2cGu5dp4PDwW58=", "FAu9s+XwYgO/I6/xSEMf9dKVDAso6xBNWrTsbXkBYS0=", "HyW/wKaxNJs9/ecBp3bzggItIU2ieRY6+l1LdNLSBtc=", "Bd5LoA6b9q12QAfCtPXJQMifTk/VLFye9Y6hhaxsKro=", "0IRumoLAvx8Sc5EAo11ItoAWNn8+ryz6ZgnO2fOF/SM=", "HeW3NDaj4fZvmLdYMkjg/GHwIbjHyTgHm0d4axQ66ek=", "opKYBoNWmBLhfu9e6LG5Ty6SO1cNVcG04B79dPjh3sk=", "QDq/1jSZco5gPagK0El+zE2dye7gYFK8MMwYjBFcgkk=", "Fs9odR/M/K6GZgV80ObO71JwN/Bh2118qaspjacK5ew=", "7JbkFEAKofXj8ECDhpie3dt8mlLQzCCjJpYDjLlMyGU=", "B74Xs+s+tAZDVeOlgH9x9ySUepnRFNPXa+Zk2pNcqKc=", "7OfbMWpQxYSmjqxxy2yBdDYC878ymQ5W1RGciaAk2SM=", "PTuQphg+qT9vux/duMb6bjHRWJDr+6gaKUGz45DBB+w=", "LBxFtU0fiFUjb6FBjbFCV+3iY3d0ueR8AZD/a1UbrQI=", "sVcgMESPwKJkodA7OW0BwM6cy7q8ytmtzol90fXzbJ4=", "ZMYredAjLMUrShPm/mYIyHb8ebeC++X4+RZrnifmroA=", "y4LUnVHOVISyeg4+XX7m6dqiKT/Zxf87537HrT5HGrE=", "ijnlEJMJOb8cluzU9hnpDn5kXSZIUssO/Wkj5jH9ZX4=", "mkUrHWqR5pgdiAsyUyi/0MoETHqF6vci5lszEweuTk8=", "6Dm/SLPmBK/cyKo6uvG9FKG6XV542lJaZIlESiSR+uQ=", "AQgH4tXGgJfJ+f0WVoLt/Eh+JL9toiaBl28CJMyOLEU=", "cFbJ8F7Tjjyt43/6f6AcMiUYE/ez5lFOjM3nhxkjW7I="], "CachedAssets": {"1GpqSYUrg1j95P4OxJpPg4s0QzsQLfFJ7f+CSNig8Wk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-08-04T09:12:56.9727112+00:00"}, "vrVoza8LjMPnst2Na97oO15pHPFR9jy7kuqRr10waII=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-08-04T09:12:57.0065007+00:00"}, "p++abtvr88olyc8WLdlHMY68swHS+ui118l6waEXb6U=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-08-04T09:12:56.9983104+00:00"}, "1AwQOgVHBBHLICG3EpiskhOZ5YegJkke3yacgjML+H8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-08-04T09:12:56.9973022+00:00"}, "3l86Vn9xeR9YHXllf9xArSp3ATAppYO6BrW5ragt+hY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-08-04T09:12:56.993155+00:00"}, "JqN+300aVLTGXWoS6SaRH7dSdnFwE+8FgYz4eaw6jok=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-08-04T09:12:56.9921548+00:00"}, "rP32TunN8/nkrC51Wye9LtiNsQF4B8ZnrNMjuoAStLU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-08-04T09:12:56.9737129+00:00"}, "YKjqP1xbsfkSLYTYRV/uRAyYQarTyw0WN+xhtl+YRbQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-08-04T09:12:57.0469552+00:00"}, "vPi/58Ht67kwofkvslGJfTBBq77B+DcjwBPmaGgFIik=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-08-04T09:12:57.0469552+00:00"}, "5R2DLPQINsn0FHl7T7FBLvVfI/Y4g0v+P8ZTqMiq0sc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-08-04T09:12:57.0459528+00:00"}, "AK1grEryw9GeRmCD3cMttd6M0r7DbD2uffqIxM81LrM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-08-04T09:12:57.0399403+00:00"}, "3PsJp1sBb/hZukwmFO52N4RZmPMfyEVhkT175xCi3og=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-08-04T09:12:56.9761103+00:00"}, "euUTd3DlZPPcd9yLjZMvm4cN0vz+QEGNd4bsj7cv90s=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-08-04T09:12:57.051198+00:00"}, "QMAFg8TRUeYQY3sQGvINvDKpfWW1zQ/S7gTCFbJ0ctk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-08-04T09:12:57.0486876+00:00"}, "CXX7kjz+0muCWDCyrTGoxNOcoMB21zmyKvQHVCE9fuk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-08-04T09:12:56.970167+00:00"}, "+R3k+S6jRMxIUv0pQ64BGlgbY7uE/XMWQofEU9zGXfw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-08-04T09:12:56.9901476+00:00"}, "nTm03V7IfvpZKa+p774Z6C4pSTcsv0o7jNF+QkU/goc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-08-04T09:12:56.9886385+00:00"}, "xWY5UB5eHGYeMW1uxtwGd9Z3H1raTo3Zl63qkupBH34=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-08-04T09:12:56.9886385+00:00"}, "nH9sW89wTUGCbWQceo54vRWvL9/tgy90nAuAdh+XJmY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-08-04T09:12:56.9877901+00:00"}, "7bnFk9etT/mbl3OcKRD+xtaS0yf9pCo59X8PTHkOPFs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-08-04T09:12:56.9862827+00:00"}, "EdkY84B/xulfVBFOvn5CtAik0MMRbqfbuMj3466KaPI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-08-04T09:12:56.9852828+00:00"}, "N4eoHemZle5WUcJPdfT0xGGM0ziZ2qi2G67KLU246Ws=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-08-04T09:12:56.9852828+00:00"}, "Ygep/D/BaO/XfZT0aDLf8TLlvAn+9F4IIRgWgyLphos=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-08-04T09:12:56.9832826+00:00"}, "CD+z1Qn+8GdLrOhPbQe7MwHutZk94emE5KPkgUHM/nM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-08-04T09:12:56.982284+00:00"}, "IAAjvzEk6+xIgEnMPsF6zM8A8nPlaXLIRPh3zSNvsFk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-08-04T09:12:56.981286+00:00"}, "b3t5wBdfN9yV2Hwr0BBUF3bhZ2pjnTB9cRvP1/lIAl0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-08-04T09:12:56.9802768+00:00"}, "S3LCBrMK5IPKQu62lXYa9cL+ZyQ+DRgwNWyvroELklI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-08-04T09:12:56.9783907+00:00"}, "WJT4GED5kVN35C/Rxg6OmCsnsq5b/THWDbFaDLDXBLA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-08-04T09:12:56.9761103+00:00"}, "bdQ+0Fg/hgWmkBboBIccLVA9XBpa86R6tzunXWE90Fw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-08-04T09:12:56.9737129+00:00"}, "ZPfVUPC1RbJCGCSFjUL3RVPokaygb58xZmyRXOlKJgs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-08-04T09:12:56.9716951+00:00"}, "VZlKpEP0NpUjFcuu3p0R9fR0lQhcoU4MCBUrhzySjE8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-08-04T09:12:56.970167+00:00"}, "7H048aZTwmjNho69Id10TXaCvC0SBG4I77naBbp7uL0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-08-04T09:12:56.9686626+00:00"}, "j/TcEgzg4I2EoEnrCnfuh0YScagDKY3mt2O73ntf0zc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-08-04T09:12:56.9676634+00:00"}, "2XlahkoO3zQ88tzBMLn0uh9E/KoWjrdTFLBXz9c9+xs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-08-04T09:12:56.9664526+00:00"}, "aX3QxH0/iSn90ShANqA0B7SGbMolBpjYXlUX38r31o0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-08-04T09:12:56.9652966+00:00"}, "Hdlti+uE020rh+yjKNcKvOapFf+kEmcaJAoqbnrQIJc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-08-04T09:12:56.9642971+00:00"}, "ccy9AumhvEKwvEhX7IsFHVimiujKEfYKGzEP+Zpjak0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-08-04T09:12:56.9632971+00:00"}, "YKYR/SoCaa2+MTPVM6/eFyE96y3auEBBe211v77o0gs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-08-04T09:12:56.9632971+00:00"}, "37DPUkwFSlnJxFU01W8IgQbEqJx4eh9tLWaKUN8ptrE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-08-04T09:12:56.9622923+00:00"}, "d8p1bJdEWoKZq01d6vgJQ/YREGc1nvLZiexTOB9pKaE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-08-04T09:12:56.9612926+00:00"}, "h85exzdjlDRVLlktUHSzJJQ46zRnOtjRLaYuo1Jftgw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-08-04T09:12:56.9602451+00:00"}, "hC+H2HN87C//JJSJdTLp4qqQlyat/hREPnp8O20duCY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-08-04T09:12:56.9602451+00:00"}, "nsWN/DMsH+tQVKqZk4OpMJw4ZsjrBY+fpiPZRi9Y0E4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-08-04T09:12:56.9585363+00:00"}, "dEOcb6ugSYsfXVrnIfADvf0WSfGmvBCt1UF0slaViY8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-08-04T09:12:56.95738+00:00"}, "3095yeRq8h9noCJMVlJKg8P0ZNDn5eDfPwkNTLlY9LE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-08-04T09:12:56.95738+00:00"}, "hkaOo1wevPVhgP41KbgJwoqNtkB1yMRUUEEeExOkPc0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-08-04T09:12:56.9561756+00:00"}, "ANYd2jvdSRmhpmXmkpiRqT0tpTjcraVXzv76az/2ILU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-08-04T09:12:56.9561756+00:00"}, "i2/4v1MlBprFT/vXzEU3my33IR8WG3Hrd8unfIQFz8s=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-08-04T09:12:56.9550526+00:00"}, "obFiMft0/kwcrPl7a56YS+bS03r1oqmKMa4KMztc7R8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-08-04T09:12:56.9550526+00:00"}, "LnqerUhBTJSa5m9zfFqKwJwuQfchUTwdbYRTsZCHTu8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-08-04T09:12:56.9550526+00:00"}, "PbX6VqUjbTWcsb3SlI2dvJrToqNEfc0PAgXCCI4Gbq0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-08-04T09:12:56.954007+00:00"}, "ZKRqJ6/X1DhK4eqhnlopqJZb7ycA0gXbK2FxHunnUAE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-08-04T09:12:56.9528124+00:00"}, "fKEqTgJ/OqwXCqNiUdReOu648qIlGdgbN0MQwtqrxV8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-08-04T09:12:56.9528124+00:00"}, "KhohUyU5DQxH4L236vtXwUa5k4eKki8Rpv4VO/ooibM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-08-04T09:12:56.9528124+00:00"}, "xFPSGednv3aCGpGXcS0oTLgzVUXihJGp8k753fZuwv4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-08-04T09:12:56.9517127+00:00"}, "BmhVFwltUHY6Z918sNQnRixFsuxEOjibNJaCXdrFJ3U=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-08-04T09:12:56.9502176+00:00"}, "Z8mKPIFxezB5WUUeKEGnkwNRwem9PI9jqLzXBUK3Hzc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-08-04T09:12:56.9502176+00:00"}, "+sQefBBt2JsRaIqOaCUb9DWdJc9l7m7h3vtjcMzaUMM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-08-04T09:12:56.9502176+00:00"}, "GEpWryBlNijdzZuD52/3Kfh37gaFoEAMjlrC4zeWj4I=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-08-04T09:12:56.9487136+00:00"}, "PH3RS0Yri96ZCuYBykcU/Tpz3NBcw1wU+ldDjnXcS6s=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\js\\site.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-08-04T09:12:57.068709+00:00"}, "stHbJU+YK+0ceinQoS3616JDt1ZLFY50uIKL3VKgvsQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\Images\\Char.png", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "Images/Char#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "to87n5mv2v", "Integrity": "Qw3l/HFTTIXXhbEfZ+YgTnRFEuA5okbYrj3Gvn9t4ZA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Images\\Char.png", "FileLength": 2348745, "LastWriteTime": "2025-08-04T10:39:22.5761445+00:00"}, "geiBVc3B0D5jvH0Dx3MXYeagSjM2ohBpxKGVtwUIef4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\favicon.ico", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-08-04T09:12:57.0459528+00:00"}, "m6sCbpJUAIZCXi867xKFgHmM/1ErpIjeT7MSoA48Jrw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\css\\site.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r1aki41k4s", "Integrity": "32GWM6c70nQTF7M5fym4+eE6cCW85W5pT53oS8bJK6Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 17653, "LastWriteTime": "2025-08-11T18:26:37.0142643+00:00"}, "rYl13VlauIgsXFksa2oqgV7wzfhwW2cBVYAr9EHuNOY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-08-04T09:12:57.0000192+00:00"}}, "CachedCopyCandidates": {}}