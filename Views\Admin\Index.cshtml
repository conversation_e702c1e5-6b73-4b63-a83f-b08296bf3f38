@{
    ViewData["Title"] = "لوحة تحكم المدير";
    var stats = ViewBag.Stats;
}

<div class="container-fluid py-4" dir="rtl">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">لوحة تحكم المدير</h1>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المستخدمين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@stats.TotalUsers</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                الموظفين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@stats.TotalEmployees.Count</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-tie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                المرضى
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@stats.TotalPatients.Count</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-injured fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                في انتظار الموافقة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@stats.PendingApprovals</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإجراءات السريعة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الإجراءات السريعة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a asp-action="CreateEmployee" class="btn btn-primary btn-block">
                                <i class="fas fa-user-plus"></i> إضافة موظف جديد
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-action="ManageUsers" class="btn btn-success btn-block">
                                <i class="fas fa-users-cog"></i> إدارة المستخدمين
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-action="MedicalRecords" class="btn btn-info btn-block">
                                <i class="fas fa-file-medical"></i> مراجعة السجلات
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-action="ManageUsers" asp-route-role="Patient" class="btn btn-warning btn-block">
                                <i class="fas fa-user-check"></i> موافقة المرضى
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- آخر الأنشطة -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إحصائيات النظام</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>إجمالي السجلات الطبية</h6>
                            <div class="progress mb-3">
                                <div class="progress-bar bg-info" role="progressbar" style="width: 100%">
                                    @stats.TotalMedicalRecords
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>المستخدمين النشطين</h6>
                            <div class="progress mb-3">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 85%">
                                    85%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-right-primary {
    border-right: 0.25rem solid #4e73df !important;
}
.border-right-success {
    border-right: 0.25rem solid #1cc88a !important;
}
.border-right-info {
    border-right: 0.25rem solid #36b9cc !important;
}
.border-right-warning {
    border-right: 0.25rem solid #f6c23e !important;
}
</style>
