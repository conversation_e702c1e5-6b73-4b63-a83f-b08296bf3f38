using HealthTrack.Models.Entities;
using System.ComponentModel.DataAnnotations;

namespace HealthTrack.Models.ViewModels
{
    public class RegisterViewModel
    {
        [Required(ErrorMessage = "الاسم الأول مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم الأول يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "الاسم الأول")]
        public string FirstName { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "الاسم الأوسط يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "الاسم الأوسط")]
        public string? MiddleName { get; set; }

        [StringLength(100, ErrorMessage = "اسم الأب يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "اسم الأب")]
        public string? FatherName { get; set; }

        [Required(ErrorMessage = "اسم العائلة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم العائلة يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "اسم العائلة")]
        public string LastName { get; set; } = string.Empty;

        [Required(ErrorMessage = "رقم الهوية مطلوب")]
        [StringLength(20, ErrorMessage = "رقم الهوية يجب أن يكون أقل من 20 رقم")]
        [Display(Name = "رقم الهوية")]
        public string NationalId { get; set; } = string.Empty;

        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "صيغة البريد الإلكتروني غير صحيحة")]
        [Display(Name = "البريد الإلكتروني")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        [Phone(ErrorMessage = "صيغة رقم الهاتف غير صحيحة")]
        [Display(Name = "رقم الهاتف")]
        public string PhoneNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "الجنس مطلوب")]
        [Display(Name = "الجنس")]
        public Gender Gender { get; set; }

        [Required(ErrorMessage = "تاريخ الميلاد مطلوب")]
        [Display(Name = "تاريخ الميلاد")]
        [DataType(DataType.Date)]
        public DateTime DateOfBirth { get; set; }

        [StringLength(200, ErrorMessage = "العنوان يجب أن يكون أقل من 200 حرف")]
        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [Display(Name = "الحالة الاجتماعية")]
        public MaritalStatus? MaritalStatus { get; set; }

        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [StringLength(100, ErrorMessage = "كلمة المرور يجب أن تكون على الأقل {2} أحرف وأقل من {1} حرف.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "كلمة المرور")]
        public string Password { get; set; } = string.Empty;

        [DataType(DataType.Password)]
        [Display(Name = "تأكيد كلمة المرور")]
        [Compare("Password", ErrorMessage = "كلمة المرور وتأكيد كلمة المرور غير متطابقتين.")]
        public string ConfirmPassword { get; set; } = string.Empty;

        // Insurance Information (Optional)
        [Display(Name = "شركة التأمين")]
        public string? InsuranceCompany { get; set; }

        [Display(Name = "رقم البوليصة")]
        public string? PolicyNumber { get; set; }

        [Display(Name = "رقم المجموعة")]
        public string? GroupNumber { get; set; }

        [Display(Name = "تاريخ انتهاء التأمين")]
        [DataType(DataType.Date)]
        public DateTime? InsuranceExpiryDate { get; set; }
    }
}
