using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HealthTrack.Models.Entities
{
    public class MedicalVisit
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int MedicalRecordId { get; set; }

        [ForeignKey("MedicalRecordId")]
        public virtual MedicalRecord MedicalRecord { get; set; } = null!;

        [Required]
        public DateTime VisitDate { get; set; }

        [StringLength(200)]
        public string? ChiefComplaint { get; set; }

        [StringLength(500)]
        public string? Symptoms { get; set; }

        [StringLength(500)]
        public string? PhysicalExamination { get; set; }

        [StringLength(500)]
        public string? Diagnosis { get; set; }

        [StringLength(500)]
        public string? Treatment { get; set; }

        [StringLength(500)]
        public string? Prescriptions { get; set; }

        [StringLength(500)]
        public string? LabOrders { get; set; }

        [StringLength(500)]
        public string? FollowUpInstructions { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        [Required]
        public string CreatedBy { get; set; } = string.Empty;

        public string? UpdatedBy { get; set; }
    }
}
