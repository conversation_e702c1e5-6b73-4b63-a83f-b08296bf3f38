@model HealthTrack.Models.ViewModels.RegisterViewModel
@{
    ViewData["Title"] = "تسجيل مريض جديد";
}

<div class="container py-4" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow">
                <div class="card-header">
                    <h4 class="mb-0">تسجيل مريض جديد</h4>
                </div>
                <div class="card-body">
                    <form asp-action="RegisterPatient" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <!-- البيانات الشخصية -->
                        <h5 class="mb-3 text-primary">البيانات الشخصية</h5>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label asp-for="FirstName" class="form-label"></label>
                                <input asp-for="FirstName" class="form-control" />
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="MiddleName" class="form-label"></label>
                                <input asp-for="MiddleName" class="form-control" />
                                <span asp-validation-for="MiddleName" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="FatherName" class="form-label"></label>
                                <input asp-for="FatherName" class="form-control" />
                                <span asp-validation-for="FatherName" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="LastName" class="form-label"></label>
                                <input asp-for="LastName" class="form-control" />
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label asp-for="NationalId" class="form-label"></label>
                                <input asp-for="NationalId" class="form-control" />
                                <span asp-validation-for="NationalId" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="Gender" class="form-label"></label>
                                <select asp-for="Gender" class="form-select">
                                    <option value="">اختر الجنس</option>
                                    <option value="1">ذكر</option>
                                    <option value="2">أنثى</option>
                                </select>
                                <span asp-validation-for="Gender" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="DateOfBirth" class="form-label"></label>
                                <input asp-for="DateOfBirth" class="form-control" type="date" />
                                <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="MaritalStatus" class="form-label"></label>
                                <select asp-for="MaritalStatus" class="form-select">
                                    <option value="">اختر الحالة الاجتماعية</option>
                                    <option value="1">أعزب</option>
                                    <option value="2">متزوج</option>
                                    <option value="3">مطلق</option>
                                    <option value="4">أرمل</option>
                                </select>
                                <span asp-validation-for="MaritalStatus" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="PhoneNumber" class="form-label"></label>
                                <input asp-for="PhoneNumber" class="form-control" />
                                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label"></label>
                            <textarea asp-for="Address" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <!-- بيانات الحساب -->
                        <h5 class="mb-3 text-primary">بيانات الحساب</h5>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label asp-for="Email" class="form-label"></label>
                                <input asp-for="Email" class="form-control" type="email" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="Password" class="form-label"></label>
                                <input asp-for="Password" class="form-control" type="password" />
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="ConfirmPassword" class="form-label"></label>
                                <input asp-for="ConfirmPassword" class="form-control" type="password" />
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- بيانات التأمين (اختيارية) -->
                        <h5 class="mb-3 text-primary">بيانات التأمين (اختيارية)</h5>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label asp-for="InsuranceCompany" class="form-label"></label>
                                <input asp-for="InsuranceCompany" class="form-control" />
                                <span asp-validation-for="InsuranceCompany" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="PolicyNumber" class="form-label"></label>
                                <input asp-for="PolicyNumber" class="form-control" />
                                <span asp-validation-for="PolicyNumber" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="GroupNumber" class="form-label"></label>
                                <input asp-for="GroupNumber" class="form-control" />
                                <span asp-validation-for="GroupNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="InsuranceExpiryDate" class="form-label"></label>
                                <input asp-for="InsuranceExpiryDate" class="form-control" type="date" />
                                <span asp-validation-for="InsuranceExpiryDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a asp-action="Index" class="btn btn-secondary me-md-2">إلغاء</a>
                            <button type="submit" class="btn btn-primary">تسجيل المريض</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
