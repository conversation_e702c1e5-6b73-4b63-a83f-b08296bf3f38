using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HealthTrack.Models.Entities
{
    public class MedicalRecord
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public string PatientId { get; set; } = string.Empty;

        [ForeignKey("PatientId")]
        public virtual ApplicationUser Patient { get; set; } = null!;

        [StringLength(500)]
        public string? ChronicDiseases { get; set; }

        [StringLength(500)]
        public string? CurrentMedications { get; set; }

        [StringLength(500)]
        public string? PreviousSurgeries { get; set; }

        [StringLength(1000)]
        public string? MedicalHistory { get; set; }

        [StringLength(500)]
        public string? Allergies { get; set; }

        [StringLength(1000)]
        public string? LabResults { get; set; }

        [StringLength(1000)]
        public string? Examinations { get; set; }

        [StringLength(1000)]
        public string? Diagnosis { get; set; }

        [StringLength(1000)]
        public string? TreatmentPlan { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        [Required]
        public string CreatedBy { get; set; } = string.Empty;

        public string? UpdatedBy { get; set; }

        // Navigation properties
        public virtual ICollection<MedicalVisit> MedicalVisits { get; set; } = new List<MedicalVisit>();
    }
}
