using HealthTrack.Data;
using HealthTrack.Models.Entities;
using HealthTrack.Models.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HealthTrack.Controllers
{
    [Authorize(Roles = "Admin")]
    public class AdminController : Controller
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ApplicationDbContext _context;

        public AdminController(UserManager<ApplicationUser> userManager, ApplicationDbContext context)
        {
            _userManager = userManager;
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            ViewData["Title"] = "لوحة تحكم المدير";
            
            var stats = new
            {
                TotalUsers = await _userManager.Users.CountAsync(),
                TotalEmployees = await _userManager.GetUsersInRoleAsync("Employee"),
                TotalPatients = await _userManager.GetUsersInRoleAsync("Patient"),
                PendingApprovals = await _userManager.Users.Where(u => !u.IsApproved).CountAsync(),
                TotalMedicalRecords = await _context.MedicalRecords.CountAsync()
            };

            ViewBag.Stats = stats;
            return View();
        }

        [HttpGet]
        public IActionResult CreateEmployee()
        {
            ViewData["Title"] = "إضافة موظف جديد";
            return View(new CreateEmployeeViewModel());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateEmployee(CreateEmployeeViewModel model)
        {
            if (!ModelState.IsValid)
            {
                ViewData["Title"] = "إضافة موظف جديد";
                return View(model);
            }

            var existingUser = await _userManager.FindByEmailAsync(model.Email);
            if (existingUser != null)
            {
                ModelState.AddModelError("Email", "البريد الإلكتروني مستخدم بالفعل");
                ViewData["Title"] = "إضافة موظف جديد";
                return View(model);
            }

            var existingNationalId = await _userManager.Users.FirstOrDefaultAsync(u => u.NationalId == model.NationalId);
            if (existingNationalId != null)
            {
                ModelState.AddModelError("NationalId", "رقم الهوية مستخدم بالفعل");
                ViewData["Title"] = "إضافة موظف جديد";
                return View(model);
            }

            var user = new ApplicationUser
            {
                UserName = model.Email,
                Email = model.Email,
                FirstName = model.FirstName,
                MiddleName = model.MiddleName,
                FatherName = model.FatherName,
                LastName = model.LastName,
                NationalId = model.NationalId,
                PhoneNumber = model.PhoneNumber,
                Gender = model.Gender,
                DateOfBirth = model.DateOfBirth,
                Address = model.Address,
                MaritalStatus = model.MaritalStatus,
                IsActive = true,
                IsApproved = model.ActivateImmediately,
                EmailConfirmed = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = User.Identity!.Name
            };

            var result = await _userManager.CreateAsync(user, model.Password);

            if (result.Succeeded)
            {
                await _userManager.AddToRoleAsync(user, "Employee");
                TempData["SuccessMessage"] = "تم إنشاء حساب الموظف بنجاح";
                return RedirectToAction("ManageUsers");
            }

            foreach (var error in result.Errors)
            {
                ModelState.AddModelError(string.Empty, error.Description);
            }

            ViewData["Title"] = "إضافة موظف جديد";
            return View(model);
        }

        public async Task<IActionResult> ManageUsers(string role = "All", int page = 1, int pageSize = 10)
        {
            ViewData["Title"] = "إدارة المستخدمين";
            
            var query = _userManager.Users.AsQueryable();

            if (role != "All")
            {
                var usersInRole = await _userManager.GetUsersInRoleAsync(role);
                var userIds = usersInRole.Select(u => u.Id).ToList();
                query = query.Where(u => userIds.Contains(u.Id));
            }

            var totalUsers = await query.CountAsync();
            var users = await query
                .OrderByDescending(u => u.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var usersWithRoles = new List<(ApplicationUser User, IList<string> Roles)>();
            foreach (var user in users)
            {
                var userRoles = await _userManager.GetRolesAsync(user);
                usersWithRoles.Add((user, userRoles));
            }

            ViewBag.UsersWithRoles = usersWithRoles;
            ViewBag.CurrentRole = role;
            ViewBag.CurrentPage = page;
            ViewBag.PageSize = pageSize;
            ViewBag.TotalPages = (int)Math.Ceiling((double)totalUsers / pageSize);

            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ToggleUserStatus(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                TempData["ErrorMessage"] = "المستخدم غير موجود";
                return RedirectToAction("ManageUsers");
            }

            user.IsActive = !user.IsActive;
            user.UpdatedAt = DateTime.UtcNow;
            user.UpdatedBy = User.Identity!.Name;

            var result = await _userManager.UpdateAsync(user);
            if (result.Succeeded)
            {
                TempData["SuccessMessage"] = user.IsActive ? "تم تفعيل المستخدم بنجاح" : "تم تعطيل المستخدم بنجاح";
            }
            else
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث حالة المستخدم";
            }

            return RedirectToAction("ManageUsers");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ApproveUser(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                TempData["ErrorMessage"] = "المستخدم غير موجود";
                return RedirectToAction("ManageUsers");
            }

            user.IsApproved = true;
            user.UpdatedAt = DateTime.UtcNow;
            user.UpdatedBy = User.Identity!.Name;

            var result = await _userManager.UpdateAsync(user);
            if (result.Succeeded)
            {
                TempData["SuccessMessage"] = "تم الموافقة على المستخدم بنجاح";
            }
            else
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء الموافقة على المستخدم";
            }

            return RedirectToAction("ManageUsers");
        }

        public async Task<IActionResult> MedicalRecords(int page = 1, int pageSize = 10)
        {
            ViewData["Title"] = "مراجعة السجلات الطبية";

            var totalRecords = await _context.MedicalRecords.CountAsync();
            var records = await _context.MedicalRecords
                .Include(m => m.Patient)
                .OrderByDescending(m => m.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            ViewBag.Records = records;
            ViewBag.CurrentPage = page;
            ViewBag.PageSize = pageSize;
            ViewBag.TotalPages = (int)Math.Ceiling((double)totalRecords / pageSize);

            return View();
        }
    }
}
