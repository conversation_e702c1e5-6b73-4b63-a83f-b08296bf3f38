@model HealthTrack.Models.LoginViewModel
@{
    ViewData["Title"] = ViewData["Title"] ?? "تسجيل الدخول";
    var returnUrl = ViewData["ReturnUrl"] as string ?? Url.Action("Index", "Home");
}

<section class="container py-5" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <h2 class="h4 mb-1">تسجيل الدخول</h2>
                        <p class="text-muted mb-0">أدخل بيانات حسابك للمتابعة</p>
                    </div>

                    @if (TempData["LoginMessage"] is string msg)
                    {
                        <div class="alert alert-success" role="alert">@msg</div>
                    }

                    <form asp-action="Login" asp-controller="Account" method="post">
                        <input type="hidden" name="returnUrl" value="@returnUrl" />
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="mb-3">
                            <label asp-for="Email" class="form-label"></label>
                            <input asp-for="Email" class="form-control" inputmode="email" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Password" class="form-label"></label>
                            <input asp-for="Password" class="form-control" />
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" asp-for="RememberMe" />
                            <label class="form-check-label" asp-for="RememberMe"></label>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">تسجيل الدخول</button>
                            <a class="btn btn-outline-secondary" href="@Url.Action("Index","Home")">العودة للصفحة الرئيسية</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}