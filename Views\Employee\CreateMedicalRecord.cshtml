@model HealthTrack.Models.Entities.MedicalRecord
@{
    ViewData["Title"] = "إنشاء سجل طبي";
    var patient = ViewBag.Patient as HealthTrack.Models.Entities.ApplicationUser;
}

<div class="container py-4" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">إنشاء سجل طبي جديد</h4>
                    @if (patient != null)
                    {
                        <p class="mb-0">المريض: @patient.FullName - @patient.NationalId</p>
                    }
                </div>
                <div class="card-body">
                    <form asp-action="CreateMedicalRecord" method="post">
                        <input type="hidden" asp-for="PatientId" />
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <!-- الأمراض المزمنة والتاريخ المرضي -->
                        <div class="section-header mb-3">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-heartbeat me-2"></i>التاريخ المرضي والأمراض المزمنة
                            </h5>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="ChronicDiseases" class="form-label">الأمراض المزمنة</label>
                                <textarea asp-for="ChronicDiseases" class="form-control" rows="4" 
                                          placeholder="مثل: السكري، ضغط الدم، أمراض القلب..."></textarea>
                                <span asp-validation-for="ChronicDiseases" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Allergies" class="form-label">الحساسية</label>
                                <textarea asp-for="Allergies" class="form-control" rows="4" 
                                          placeholder="حساسية من أدوية، أطعمة، مواد معينة..."></textarea>
                                <span asp-validation-for="Allergies" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="CurrentMedications" class="form-label">الأدوية الحالية</label>
                                <textarea asp-for="CurrentMedications" class="form-control" rows="4" 
                                          placeholder="الأدوية التي يتناولها المريض حالياً..."></textarea>
                                <span asp-validation-for="CurrentMedications" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="PreviousSurgeries" class="form-label">العمليات السابقة</label>
                                <textarea asp-for="PreviousSurgeries" class="form-control" rows="4" 
                                          placeholder="العمليات الجراحية السابقة وتواريخها..."></textarea>
                                <span asp-validation-for="PreviousSurgeries" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="MedicalHistory" class="form-label">التاريخ المرضي التفصيلي</label>
                            <textarea asp-for="MedicalHistory" class="form-control" rows="5" 
                                      placeholder="تاريخ مرضي مفصل، أمراض سابقة، تاريخ عائلي..."></textarea>
                            <span asp-validation-for="MedicalHistory" class="text-danger"></span>
                        </div>

                        <!-- الفحوصات والتشخيص -->
                        <div class="section-header mb-3 mt-4">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-stethoscope me-2"></i>الفحوصات والتشخيص
                            </h5>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Examinations" class="form-label">الفحوصات السريرية</label>
                                <textarea asp-for="Examinations" class="form-control" rows="4" 
                                          placeholder="نتائج الفحص السريري..."></textarea>
                                <span asp-validation-for="Examinations" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="LabResults" class="form-label">نتائج التحاليل والفحوصات</label>
                                <textarea asp-for="LabResults" class="form-control" rows="4" 
                                          placeholder="نتائج التحاليل المخبرية والأشعة..."></textarea>
                                <span asp-validation-for="LabResults" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Diagnosis" class="form-label">التشخيص</label>
                                <textarea asp-for="Diagnosis" class="form-control" rows="4" 
                                          placeholder="التشخيص النهائي للحالة..."></textarea>
                                <span asp-validation-for="Diagnosis" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="TreatmentPlan" class="form-label">خطة العلاج</label>
                                <textarea asp-for="TreatmentPlan" class="form-control" rows="4" 
                                          placeholder="خطة العلاج المقترحة..."></textarea>
                                <span asp-validation-for="TreatmentPlan" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- ملاحظات إضافية -->
                        <div class="section-header mb-3 mt-4">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-sticky-note me-2"></i>ملاحظات إضافية
                            </h5>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Notes" class="form-label">ملاحظات</label>
                            <textarea asp-for="Notes" class="form-control" rows="3" 
                                      placeholder="أي ملاحظات إضافية..."></textarea>
                            <span asp-validation-for="Notes" class="text-danger"></span>
                        </div>

                        <!-- تنبيه مهم -->
                        <div class="alert alert-warning" role="alert">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>تنبيه مهم
                            </h6>
                            <p class="mb-0">
                                تأكد من دقة جميع المعلومات المدخلة. لا يمكن تعديل السجل الطبي بعد الحفظ.
                                جميع البيانات ستكون متاحة للمريض للاطلاع عليها.
                            </p>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a asp-action="PatientsList" class="btn btn-secondary me-md-2">
                                <i class="fas fa-arrow-left me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>حفظ السجل الطبي
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.section-header h5 {
    position: relative;
}

.section-header h5::before {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, #007bff, transparent);
}
</style>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
